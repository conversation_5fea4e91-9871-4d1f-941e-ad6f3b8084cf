{% extends 'base.html' %}
{% load static %}

{% block title %}Notifications - Library Management System{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Notifications</h1>
                <p class="text-gray-600 mt-2">Stay updated with your library activities</p>
            </div>
            <div class="flex space-x-4">
                <button id="mark-all-read" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-sm">
                    <i class="fas fa-check-double mr-2"></i>Mark All Read
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-bell text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Notifications</p>
                    <p class="text-2xl font-bold text-gray-900">{{ page_obj.paginator.count }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-circle text-orange-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Unread</p>
                    <p class="text-2xl font-bold text-gray-900">{{ notifications|length|add:"-1" }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Read</p>
                    <p class="text-2xl font-bold text-gray-900">{{ notifications|length|add:"-1" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">All Notifications</h2>
        </div>
        
        {% if notifications %}
        <div class="divide-y divide-gray-200">
            {% for notification in notifications %}
            <div class="p-6 hover:bg-gray-50 transition-colors duration-200 {% if not notification.is_read %}bg-blue-50{% endif %}" 
                 data-notification-id="{{ notification.id }}">
                <div class="flex items-start justify-between">
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center
                                {% if notification.notification_type == 'book_due' %}bg-yellow-100{% endif %}
                                {% if notification.notification_type == 'book_overdue' %}bg-red-100{% endif %}
                                {% if notification.notification_type == 'book_returned' %}bg-green-100{% endif %}
                                {% if notification.notification_type == 'request_approved' %}bg-green-100{% endif %}
                                {% if notification.notification_type == 'request_rejected' %}bg-red-100{% endif %}
                                {% if notification.notification_type == 'general' %}bg-blue-100{% endif %}">
                                {% if notification.notification_type == 'book_due' %}
                                    <i class="fas fa-clock text-yellow-600"></i>
                                {% elif notification.notification_type == 'book_overdue' %}
                                    <i class="fas fa-exclamation-triangle text-red-600"></i>
                                {% elif notification.notification_type == 'book_returned' %}
                                    <i class="fas fa-check-circle text-green-600"></i>
                                {% elif notification.notification_type == 'request_approved' %}
                                    <i class="fas fa-thumbs-up text-green-600"></i>
                                {% elif notification.notification_type == 'request_rejected' %}
                                    <i class="fas fa-thumbs-down text-red-600"></i>
                                {% else %}
                                    <i class="fas fa-bell text-blue-600"></i>
                                {% endif %}
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center space-x-2">
                                <h3 class="text-sm font-medium text-gray-900">{{ notification.title }}</h3>
                                {% if not notification.is_read %}
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                {% endif %}
                            </div>
                            <p class="text-sm text-gray-600 mt-1">{{ notification.message }}</p>
                            <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                <span>
                                    <i class="fas fa-calendar mr-1"></i>
                                    {{ notification.created_at|date:"M d, Y" }}
                                </span>
                                <span>
                                    <i class="fas fa-clock mr-1"></i>
                                    {{ notification.created_at|date:"g:i A" }}
                                </span>
                                {% if notification.book %}
                                <span>
                                    <i class="fas fa-book mr-1"></i>
                                    {{ notification.book.title|truncatechars:30 }}
                                </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col items-end space-y-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {% if notification.notification_type == 'book_due' %}bg-yellow-100 text-yellow-800{% endif %}
                            {% if notification.notification_type == 'book_overdue' %}bg-red-100 text-red-800{% endif %}
                            {% if notification.notification_type == 'book_returned' %}bg-green-100 text-green-800{% endif %}
                            {% if notification.notification_type == 'request_approved' %}bg-green-100 text-green-800{% endif %}
                            {% if notification.notification_type == 'request_rejected' %}bg-red-100 text-red-800{% endif %}
                            {% if notification.notification_type == 'general' %}bg-blue-100 text-blue-800{% endif %}">
                            {{ notification.get_notification_type_display }}
                        </span>
                        {% if not notification.is_read %}
                        <button onclick="markAsRead({{ notification.id }})" class="text-blue-600 hover:text-blue-800 text-xs font-medium">
                            Mark as read
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Previous</a>
                {% endif %}
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Next</a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">Previous</a>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">{{ num }}</span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">{{ num }}</a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">Next</a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-12">
            <i class="fas fa-bell text-gray-300 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Notifications</h3>
            <p class="text-gray-500">You don't have any notifications yet.</p>
        </div>
        {% endif %}
    </div>
</div>

<script>
function markAsRead(notificationId) {
    fetch(`/api/notifications/${notificationId}/read/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload(); // Refresh the page to update the UI
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
    });
}

function markAllAsRead() {
    fetch('/api/notifications/mark-all-read/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload(); // Refresh the page to update the UI
        }
    })
    .catch(error => {
        console.error('Error marking all notifications as read:', error);
    });
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Set up event listeners
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('mark-all-read').addEventListener('click', markAllAsRead);
});
</script>
{% endblock %}
