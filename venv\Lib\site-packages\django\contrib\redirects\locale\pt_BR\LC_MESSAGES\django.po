# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2014
# <PERSON> <amanda<PERSON>v<PERSON><PERSON><EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2024
# fa9e10542e458baef0599ae856e43651_13d2225, 2013
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2021
# <PERSON>.<PERSON> <<EMAIL>>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2024-08-07 18:32+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Portuguese (Brazil) (http://app.transifex.com/django/django/"
"language/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Redirects"
msgstr "Redirecionamentos"

msgid "site"
msgstr "site"

msgid "redirect from"
msgstr "redirecionar de"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Este deve ser um caminho absoluto, excluindo o domínio. Exemplo: \"/events/"
"search/\"."

msgid "redirect to"
msgstr "redirecionar para"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Este pode ser um caminho absoluto (como acima) ou uma URL completa começando "
"com um esquema como \"https://\"."

msgid "redirect"
msgstr "redirecionamento"

msgid "redirects"
msgstr "redirecionamentos"
