{% extends 'base.html' %}
{% load library_extras %}

{% block title %}Add New User - LibraryPro{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">User Management</h1>
            <p class="mt-1 text-sm text-gray-500">Add new users individually or import from CSV</p>
        </div>
        <a href="{% url 'user_list' %}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Users
        </a>
    </div>

    <!-- Tab Navigation -->
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8">
            <button onclick="showTab('single')" id="single-tab"
                    class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                <i class="fas fa-user mr-2"></i>
                Single User
            </button>
            <button onclick="showTab('csv')" id="csv-tab"
                    class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                <i class="fas fa-file-csv mr-2"></i>
                CSV Import
            </button>
            <button onclick="showTab('api')" id="api-tab"
                    class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                <i class="fas fa-cloud mr-2"></i>
                API Integration
            </button>
        </nav>
    </div>

    <!-- Single User Form -->
    <div id="single-content" class="tab-content">
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Add Individual User</h3>
                <p class="mt-1 text-sm text-gray-500">Create a single user account</p>
            </div>

            <form method="post" class="divide-y divide-gray-200">
                {% csrf_token %}
            
            <!-- Account Information -->
            <div class="space-y-6">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-key text-blue-600 mr-2"></i>
                    Account Information
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Username *
                        </label>
                        {{ form.username }}
                        {% if form.username.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.username.errors.0 }}
                        </div>
                        {% endif %}
                        <div class="mt-1 text-sm text-gray-500">
                            <i class="fas fa-info-circle mr-1"></i>
                            Used for login. Must be unique.
                        </div>
                    </div>
                    
                    <div>
                        <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address *
                        </label>
                        {{ form.email }}
                        {% if form.email.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.email.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Password *
                        </label>
                        {{ form.password1 }}
                        {% if form.password1.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.password1.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Confirm Password *
                        </label>
                        {{ form.password2 }}
                        {% if form.password2.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.password2.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Personal Information -->
            <div class="space-y-6 border-t border-gray-200 pt-8">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-user text-purple-600 mr-2"></i>
                    Personal Information
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            First Name *
                        </label>
                        {{ form.first_name }}
                        {% if form.first_name.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.first_name.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Last Name *
                        </label>
                        {{ form.last_name }}
                        {% if form.last_name.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.last_name.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number
                        </label>
                        {{ form.phone_number }}
                        {% if form.phone_number.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.phone_number.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.date_of_birth.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Date of Birth
                        </label>
                        {{ form.date_of_birth }}
                        {% if form.date_of_birth.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.date_of_birth.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="md:col-span-2">
                        <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Address
                        </label>
                        {{ form.address }}
                        {% if form.address.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.address.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Role and Academic Information -->
            <div class="space-y-6 border-t border-gray-200 pt-8">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-graduation-cap text-orange-600 mr-2"></i>
                    Role & Academic Information
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="{{ form.role.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Role *
                        </label>
                        {{ form.role }}
                        {% if form.role.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.role.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.enrollment_number.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Enrollment/ID Number
                        </label>
                        {{ form.enrollment_number }}
                        {% if form.enrollment_number.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.enrollment_number.errors.0 }}
                        </div>
                        {% endif %}
                        <div class="mt-1 text-sm text-gray-500">
                            <i class="fas fa-info-circle mr-1"></i>
                            For students and staff identification
                        </div>
                    </div>
                    
                    <div>
                        <label for="{{ form.class_grade.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Class/Grade
                        </label>
                        {{ form.class_grade }}
                        {% if form.class_grade.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.class_grade.errors.0 }}
                        </div>
                        {% endif %}
                        <div class="mt-1 text-sm text-gray-500">
                            <i class="fas fa-info-circle mr-1"></i>
                            For students only
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-4 pt-8 border-t border-gray-200">
                <a href="{% url 'user_list' %}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-8 py-3 rounded-xl font-medium transition-all duration-300">
                    <i class="fas fa-times mr-2"></i>Cancel
                </a>
                <button type="submit" 
                        class="btn-primary px-8 py-3 rounded-xl text-white font-medium shadow-lg">
                    <i class="fas fa-user-plus mr-2"></i>Create User
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Add form control classes to Django form fields
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        if (!input.classList.contains('btn')) {
            input.classList.add('w-full', 'px-4', 'py-3', 'border', 'border-gray-300', 'rounded-xl', 'focus:ring-2', 'focus:ring-blue-500', 'focus:border-transparent', 'transition-all', 'duration-300');
        }
    });
    
    // Show/hide academic fields based on role
    const roleSelect = document.getElementById('{{ form.role.id_for_label }}');
    const enrollmentField = document.getElementById('{{ form.enrollment_number.id_for_label }}').closest('div');
    const classField = document.getElementById('{{ form.class_grade.id_for_label }}').closest('div');
    
    function toggleAcademicFields() {
        const role = roleSelect.value;
        if (role === 'student') {
            enrollmentField.style.display = 'block';
            classField.style.display = 'block';
        } else if (role === 'teacher') {
            enrollmentField.style.display = 'block';
            classField.style.display = 'none';
        } else {
            enrollmentField.style.display = 'none';
            classField.style.display = 'none';
        }
    }
    
    roleSelect.addEventListener('change', toggleAcademicFields);
    toggleAcademicFields(); // Initial call
});
</script>
{% endblock %}
