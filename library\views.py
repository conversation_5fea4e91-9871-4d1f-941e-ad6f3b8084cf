from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, logout
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from datetime import timedelta
import json

from .models import User, Book, Author, Category, Publisher, Loan, Reservation, LibrarySettings, AuditLog
from .forms import (CustomUserCreationForm, CustomAuthenticationForm, UserProfileForm,
                   BookForm, BookSearchForm, LoanForm, ReservationForm)


def is_admin(user):
    """Check if user is admin"""
    return user.is_authenticated and user.role == 'admin'


def is_librarian_or_admin(user):
    """Check if user is librarian or admin"""
    return user.is_authenticated and user.role in ['admin', 'librarian']


def can_manage_books(user):
    """Check if user can manage books"""
    return user.is_authenticated and user.can_manage_books()


def home(request):
    """Home page with dashboard"""
    if not request.user.is_authenticated:
        return redirect('login')

    context = {
        'total_books': Book.objects.count(),
        'available_books': Book.objects.filter(status='available').count(),
        'total_users': User.objects.filter(is_active_member=True).count(),
        'active_loans': Loan.objects.filter(status='active').count(),
        'overdue_loans': Loan.objects.filter(status='overdue').count(),
    }

    # Recent activities for admins/librarians
    if request.user.can_manage_books():
        context['recent_loans'] = Loan.objects.select_related('book', 'borrower').order_by('-issue_date')[:5]
        context['recent_reservations'] = Reservation.objects.select_related('book', 'user').filter(status='active').order_by('-reservation_date')[:5]

    # User's personal data
    if request.user.can_borrow_books():
        context['my_loans'] = Loan.objects.filter(borrower=request.user, status='active').select_related('book')
        context['my_reservations'] = Reservation.objects.filter(user=request.user, status='active').select_related('book')

    return render(request, 'library/home.html', context)


def custom_login(request):
    """Custom login view"""
    if request.user.is_authenticated:
        return redirect('home')

    if request.method == 'POST':
        form = CustomAuthenticationForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()
            login(request, user)

            # Log the login
            AuditLog.objects.create(
                action='user_login',
                user=user,
                description=f"User {user.username} logged in",
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'Welcome back, {user.get_full_name()}!')
            next_url = request.GET.get('next', 'home')
            return redirect(next_url)
    else:
        form = CustomAuthenticationForm()

    return render(request, 'registration/login.html', {'form': form})


def custom_logout(request):
    """Custom logout view"""
    if request.user.is_authenticated:
        # Log the logout
        AuditLog.objects.create(
            action='user_logout',
            user=request.user,
            description=f"User {request.user.username} logged out",
            ip_address=request.META.get('REMOTE_ADDR')
        )

    logout(request)
    messages.info(request, 'You have been logged out successfully.')
    return redirect('login')


@user_passes_test(is_admin)
def register_user(request):
    """Register new user (admin only)"""
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST, user=request.user)
        if form.is_valid():
            user = form.save()

            # Log the user creation
            AuditLog.objects.create(
                action='user_created',
                user=request.user,
                target_user=user,
                description=f"User {user.username} created by {request.user.username}",
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'User {user.username} created successfully!')
            return redirect('user_list')
    else:
        form = CustomUserCreationForm(user=request.user)

    return render(request, 'library/register.html', {'form': form})


@login_required
def profile(request):
    """User profile view"""
    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=request.user)
        if form.is_valid():
            form.save()

            # Log the profile update
            AuditLog.objects.create(
                action='user_updated',
                user=request.user,
                description=f"User {request.user.username} updated their profile",
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, 'Profile updated successfully!')
            return redirect('profile')
    else:
        form = UserProfileForm(instance=request.user)

    # Get user's loan and reservation history
    loans = Loan.objects.filter(borrower=request.user).select_related('book').order_by('-issue_date')
    reservations = Reservation.objects.filter(user=request.user).select_related('book').order_by('-reservation_date')

    context = {
        'form': form,
        'loans': loans[:10],  # Last 10 loans
        'reservations': reservations[:10],  # Last 10 reservations
    }

    return render(request, 'library/profile.html', context)


def book_list(request):
    """List all books with search and filtering"""
    form = BookSearchForm(request.GET)
    books = Book.objects.select_related('publisher').prefetch_related('authors', 'categories')

    if form.is_valid():
        query = form.cleaned_data.get('query')
        category = form.cleaned_data.get('category')
        status = form.cleaned_data.get('status')
        section = form.cleaned_data.get('section')

        if query:
            books = books.filter(
                Q(title__icontains=query) |
                Q(authors__first_name__icontains=query) |
                Q(authors__last_name__icontains=query) |
                Q(isbn__icontains=query) |
                Q(publisher__name__icontains=query)
            ).distinct()

        if category:
            books = books.filter(categories=category)

        if status:
            books = books.filter(status=status)

        if section:
            books = books.filter(section__icontains=section)

    # Pagination
    paginator = Paginator(books, 12)  # 12 books per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'form': form,
        'page_obj': page_obj,
        'books': page_obj,
    }

    return render(request, 'library/book_list.html', context)


def book_detail(request, book_id):
    """Book detail view"""
    book = get_object_or_404(Book, id=book_id)

    # Check if user can reserve this book
    can_reserve = False
    if request.user.is_authenticated and request.user.can_borrow_books():
        can_reserve = (
            not book.is_available() and
            not Reservation.objects.filter(
                book=book,
                user=request.user,
                status='active'
            ).exists()
        )

    # Get active reservations for this book
    reservations = Reservation.objects.filter(
        book=book,
        status='active'
    ).select_related('user').order_by('priority')

    # Get loan history (for librarians/admins)
    loan_history = None
    if request.user.is_authenticated and request.user.can_manage_books():
        loan_history = Loan.objects.filter(book=book).select_related('borrower').order_by('-issue_date')[:10]

    context = {
        'book': book,
        'can_reserve': can_reserve,
        'reservations': reservations,
        'loan_history': loan_history,
    }

    return render(request, 'library/book_detail.html', context)


@user_passes_test(can_manage_books)
def book_add(request):
    """Add new book"""
    if request.method == 'POST':
        form = BookForm(request.POST, request.FILES)
        if form.is_valid():
            book = form.save(commit=False)
            book.created_by = request.user
            book.save()
            form.save_m2m()

            # Log the book addition
            AuditLog.objects.create(
                action='book_added',
                user=request.user,
                book=book,
                description=f"Book '{book.title}' added by {request.user.username}",
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'Book "{book.title}" added successfully!')
            return redirect('book_detail', book_id=book.id)
    else:
        form = BookForm()

    return render(request, 'library/book_form.html', {'form': form, 'title': 'Add Book'})


@user_passes_test(can_manage_books)
def book_edit(request, book_id):
    """Edit existing book"""
    book = get_object_or_404(Book, id=book_id)

    if request.method == 'POST':
        form = BookForm(request.POST, request.FILES, instance=book)
        if form.is_valid():
            book = form.save()

            # Log the book update
            AuditLog.objects.create(
                action='book_updated',
                user=request.user,
                book=book,
                description=f"Book '{book.title}' updated by {request.user.username}",
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'Book "{book.title}" updated successfully!')
            return redirect('book_detail', book_id=book.id)
    else:
        form = BookForm(instance=book)

    return render(request, 'library/book_form.html', {'form': form, 'title': 'Edit Book', 'book': book})


@user_passes_test(can_manage_books)
def book_delete(request, book_id):
    """Delete book"""
    book = get_object_or_404(Book, id=book_id)

    if request.method == 'POST':
        # Check if book has active loans
        if Loan.objects.filter(book=book, status='active').exists():
            messages.error(request, 'Cannot delete book with active loans.')
            return redirect('book_detail', book_id=book.id)

        book_title = book.title

        # Log the book deletion
        AuditLog.objects.create(
            action='book_deleted',
            user=request.user,
            description=f"Book '{book_title}' deleted by {request.user.username}",
            ip_address=request.META.get('REMOTE_ADDR')
        )

        book.delete()
        messages.success(request, f'Book "{book_title}" deleted successfully!')
        return redirect('book_list')

    return render(request, 'library/book_confirm_delete.html', {'book': book})


@user_passes_test(is_admin)
def user_list(request):
    """List all users (admin only)"""
    users = User.objects.all().order_by('-date_joined')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(enrollment_number__icontains=search_query)
        )

    # Role filter
    role_filter = request.GET.get('role', '')
    if role_filter:
        users = users.filter(role=role_filter)

    # Pagination
    paginator = Paginator(users, 20)  # 20 users per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'users': page_obj,
        'search_query': search_query,
        'role_filter': role_filter,
        'role_choices': User.ROLE_CHOICES,
    }

    return render(request, 'library/user_list.html', context)


@user_passes_test(is_admin)
def user_detail(request, user_id):
    """User detail view (admin only)"""
    user_obj = get_object_or_404(User, id=user_id)

    # Get user's loan history
    loans = Loan.objects.filter(borrower=user_obj).select_related('book').order_by('-issue_date')

    # Get user's reservation history
    reservations = Reservation.objects.filter(user=user_obj).select_related('book').order_by('-reservation_date')

    # Get statistics
    stats = {
        'total_loans': loans.count(),
        'active_loans': loans.filter(status='active').count(),
        'overdue_loans': loans.filter(status='overdue').count(),
        'total_reservations': reservations.count(),
        'active_reservations': reservations.filter(status='active').count(),
    }

    context = {
        'user_obj': user_obj,
        'loans': loans[:10],  # Last 10 loans
        'reservations': reservations[:10],  # Last 10 reservations
        'stats': stats,
    }

    return render(request, 'library/user_detail.html', context)


@user_passes_test(is_admin)
def user_edit(request, user_id):
    """Edit user (admin only)"""
    user_obj = get_object_or_404(User, id=user_id)

    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=user_obj)
        if form.is_valid():
            form.save()

            # Log the user update
            AuditLog.objects.create(
                action='user_updated',
                user=request.user,
                target_user=user_obj,
                description=f"User {user_obj.username} updated by {request.user.username}",
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'User {user_obj.username} updated successfully!')
            return redirect('user_detail', user_id=user_obj.id)
    else:
        form = UserProfileForm(instance=user_obj)

    return render(request, 'library/user_form.html', {'form': form, 'user_obj': user_obj, 'title': 'Edit User'})


@user_passes_test(is_admin)
def user_toggle_status(request, user_id):
    """Toggle user active status (admin only)"""
    user_obj = get_object_or_404(User, id=user_id)

    if request.method == 'POST':
        user_obj.is_active_member = not user_obj.is_active_member
        user_obj.save()

        status = "activated" if user_obj.is_active_member else "deactivated"

        # Log the status change
        AuditLog.objects.create(
            action='user_updated',
            user=request.user,
            target_user=user_obj,
            description=f"User {user_obj.username} {status} by {request.user.username}",
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, f'User {user_obj.username} has been {status}.')

    return redirect('user_detail', user_id=user_obj.id)


@user_passes_test(is_librarian_or_admin)
def loan_list(request):
    """List all loans"""
    loans = Loan.objects.select_related('book', 'borrower').order_by('-issue_date')

    # Filter by status
    status_filter = request.GET.get('status', '')
    if status_filter:
        loans = loans.filter(status=status_filter)

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        loans = loans.filter(
            Q(book__title__icontains=search_query) |
            Q(borrower__username__icontains=search_query) |
            Q(borrower__first_name__icontains=search_query) |
            Q(borrower__last_name__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(loans, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'loans': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'status_choices': Loan.STATUS_CHOICES,
    }

    return render(request, 'library/loan_list.html', context)


@user_passes_test(is_librarian_or_admin)
def loan_create(request):
    """Create new loan"""
    if request.method == 'POST':
        form = LoanForm(request.POST)
        if form.is_valid():
            loan = form.save(commit=False)
            loan.issued_by = request.user

            # Check if book is available
            book = loan.book
            if not book.is_available():
                messages.error(request, 'This book is not available for loan.')
                return render(request, 'library/loan_form.html', {'form': form, 'title': 'Issue Loan'})

            # Update book availability
            book.available_copies -= 1
            book.update_availability()

            loan.save()

            # Log the loan
            AuditLog.objects.create(
                action='loan_issued',
                user=request.user,
                target_user=loan.borrower,
                book=loan.book,
                description=f"Book '{loan.book.title}' issued to {loan.borrower.get_full_name()} by {request.user.username}",
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'Book "{loan.book.title}" issued to {loan.borrower.get_full_name()} successfully!')
            return redirect('loan_list')
    else:
        form = LoanForm()

    return render(request, 'library/loan_form.html', {'form': form, 'title': 'Issue Loan'})


@user_passes_test(is_librarian_or_admin)
def loan_return(request, loan_id):
    """Return a book"""
    loan = get_object_or_404(Loan, id=loan_id)

    if request.method == 'POST':
        if loan.status != 'active':
            messages.error(request, 'This loan is not active.')
            return redirect('loan_list')

        # Mark loan as returned
        loan.return_date = timezone.now()
        loan.status = 'returned'
        loan.save()

        # Update book availability
        book = loan.book
        book.available_copies += 1
        book.update_availability()

        # Check for reservations and notify
        next_reservation = Reservation.objects.filter(
            book=book,
            status='active'
        ).order_by('priority').first()

        if next_reservation:
            # Here you could send notification to the user
            messages.info(request, f'Book is reserved for {next_reservation.user.get_full_name()}.')

        # Log the return
        AuditLog.objects.create(
            action='loan_returned',
            user=request.user,
            target_user=loan.borrower,
            book=loan.book,
            description=f"Book '{loan.book.title}' returned by {loan.borrower.get_full_name()}",
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, f'Book "{loan.book.title}" returned successfully!')
        return redirect('loan_list')

    return render(request, 'library/loan_return_confirm.html', {'loan': loan})


@login_required
def reservation_create(request, book_id):
    """Create a reservation for a book"""
    book = get_object_or_404(Book, id=book_id)

    if not request.user.can_borrow_books():
        messages.error(request, 'You do not have permission to reserve books.')
        return redirect('book_detail', book_id=book.id)

    # Check if book is available
    if book.is_available():
        messages.error(request, 'This book is currently available. You can borrow it directly.')
        return redirect('book_detail', book_id=book.id)

    # Check if user already has a reservation for this book
    if Reservation.objects.filter(book=book, user=request.user, status='active').exists():
        messages.error(request, 'You already have an active reservation for this book.')
        return redirect('book_detail', book_id=book.id)

    if request.method == 'POST':
        form = ReservationForm(request.POST, user=request.user)
        if form.is_valid():
            reservation = form.save(commit=False)
            reservation.user = request.user
            reservation.book = book

            # Set priority (next in queue)
            last_reservation = Reservation.objects.filter(
                book=book,
                status='active'
            ).order_by('-priority').first()

            reservation.priority = (last_reservation.priority + 1) if last_reservation else 1
            reservation.save()

            # Log the reservation
            AuditLog.objects.create(
                action='reservation_made',
                user=request.user,
                book=book,
                description=f"Book '{book.title}' reserved by {request.user.get_full_name()}",
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'Book "{book.title}" reserved successfully! You are #{ reservation.priority} in the queue.')
            return redirect('book_detail', book_id=book.id)
    else:
        form = ReservationForm(user=request.user)
        form.fields['book'].initial = book
        form.fields['book'].widget.attrs['readonly'] = True

    return render(request, 'library/reservation_form.html', {'form': form, 'book': book})


@login_required
def reservation_cancel(request, reservation_id):
    """Cancel a reservation"""
    reservation = get_object_or_404(Reservation, id=reservation_id)

    # Check permissions
    if reservation.user != request.user and not request.user.can_manage_books():
        messages.error(request, 'You do not have permission to cancel this reservation.')
        return redirect('profile')

    if request.method == 'POST':
        if reservation.status != 'active':
            messages.error(request, 'This reservation is not active.')
            return redirect('profile')

        book = reservation.book
        reservation.status = 'cancelled'
        reservation.save()

        # Update priorities for remaining reservations
        remaining_reservations = Reservation.objects.filter(
            book=book,
            status='active',
            priority__gt=reservation.priority
        ).order_by('priority')

        for i, res in enumerate(remaining_reservations, start=reservation.priority):
            res.priority = i
            res.save()

        # Log the cancellation
        AuditLog.objects.create(
            action='reservation_cancelled',
            user=request.user,
            target_user=reservation.user,
            book=book,
            description=f"Reservation for '{book.title}' cancelled by {request.user.get_full_name()}",
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, f'Reservation for "{book.title}" cancelled successfully!')

        if request.user.can_manage_books():
            return redirect('book_detail', book_id=book.id)
        else:
            return redirect('profile')

    return render(request, 'library/reservation_cancel_confirm.html', {'reservation': reservation})


def book_list(request):
    """List all books with search and filtering"""
    form = BookSearchForm(request.GET)
    books = Book.objects.select_related('publisher').prefetch_related('authors', 'categories')

    if form.is_valid():
        query = form.cleaned_data.get('query')
        category = form.cleaned_data.get('category')
        status = form.cleaned_data.get('status')
        section = form.cleaned_data.get('section')

        if query:
            books = books.filter(
                Q(title__icontains=query) |
                Q(authors__first_name__icontains=query) |
                Q(authors__last_name__icontains=query) |
                Q(isbn__icontains=query) |
                Q(publisher__name__icontains=query)
            ).distinct()

        if category:
            books = books.filter(categories=category)

        if status:
            books = books.filter(status=status)

        if section:
            books = books.filter(section__icontains=section)

    # Pagination
    paginator = Paginator(books, 12)  # 12 books per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'form': form,
        'page_obj': page_obj,
        'books': page_obj,
    }

    return render(request, 'library/book_list.html', context)


def book_detail(request, book_id):
    """Book detail view"""
    book = get_object_or_404(Book, id=book_id)

    # Check if user can reserve this book
    can_reserve = False
    if request.user.is_authenticated and request.user.can_borrow_books():
        can_reserve = (
            not book.is_available() and
            not Reservation.objects.filter(
                book=book,
                user=request.user,
                status='active'
            ).exists()
        )

    # Get active reservations for this book
    reservations = Reservation.objects.filter(
        book=book,
        status='active'
    ).select_related('user').order_by('priority')

    # Get loan history (for librarians/admins)
    loan_history = None
    if request.user.is_authenticated and request.user.can_manage_books():
        loan_history = Loan.objects.filter(book=book).select_related('borrower').order_by('-issue_date')[:10]

    context = {
        'book': book,
        'can_reserve': can_reserve,
        'reservations': reservations,
        'loan_history': loan_history,
    }

    return render(request, 'library/book_detail.html', context)


@user_passes_test(can_manage_books)
def book_add(request):
    """Add new book"""
    if request.method == 'POST':
        form = BookForm(request.POST, request.FILES)
        if form.is_valid():
            book = form.save(commit=False)
            book.created_by = request.user
            book.save()
            form.save_m2m()

            # Log the book addition
            AuditLog.objects.create(
                action='book_added',
                user=request.user,
                book=book,
                description=f"Book '{book.title}' added by {request.user.username}",
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'Book "{book.title}" added successfully!')
            return redirect('book_detail', book_id=book.id)
    else:
        form = BookForm()

    return render(request, 'library/book_form.html', {'form': form, 'title': 'Add Book'})


@user_passes_test(can_manage_books)
def book_edit(request, book_id):
    """Edit existing book"""
    book = get_object_or_404(Book, id=book_id)

    if request.method == 'POST':
        form = BookForm(request.POST, request.FILES, instance=book)
        if form.is_valid():
            book = form.save()

            # Log the book update
            AuditLog.objects.create(
                action='book_updated',
                user=request.user,
                book=book,
                description=f"Book '{book.title}' updated by {request.user.username}",
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'Book "{book.title}" updated successfully!')
            return redirect('book_detail', book_id=book.id)
    else:
        form = BookForm(instance=book)

    return render(request, 'library/book_form.html', {'form': form, 'title': 'Edit Book', 'book': book})


@user_passes_test(can_manage_books)
def book_delete(request, book_id):
    """Delete book"""
    book = get_object_or_404(Book, id=book_id)

    if request.method == 'POST':
        # Check if book has active loans
        if Loan.objects.filter(book=book, status='active').exists():
            messages.error(request, 'Cannot delete book with active loans.')
            return redirect('book_detail', book_id=book.id)

        book_title = book.title

        # Log the book deletion
        AuditLog.objects.create(
            action='book_deleted',
            user=request.user,
            description=f"Book '{book_title}' deleted by {request.user.username}",
            ip_address=request.META.get('REMOTE_ADDR')
        )

        book.delete()
        messages.success(request, f'Book "{book_title}" deleted successfully!')
        return redirect('book_list')

    return render(request, 'library/book_confirm_delete.html', {'book': book})
