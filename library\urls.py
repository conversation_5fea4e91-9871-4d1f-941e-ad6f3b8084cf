from django.urls import path
from . import views

urlpatterns = [
    # Authentication
    path('login/', views.custom_login, name='login'),
    path('logout/', views.custom_logout, name='logout'),
    path('register/', views.register_user, name='register'),
    path('profile/', views.profile, name='profile'),

    # Home
    path('', views.home, name='home'),

    # Books
    path('books/', views.book_list, name='book_list'),
    path('books/<uuid:book_id>/', views.book_detail, name='book_detail'),
    path('books/add/', views.book_add, name='book_add'),
    path('books/<uuid:book_id>/edit/', views.book_edit, name='book_edit'),
    path('books/<uuid:book_id>/delete/', views.book_delete, name='book_delete'),

    # Users (Admin only)
    path('users/', views.user_list, name='user_list'),
    path('users/<uuid:user_id>/', views.user_detail, name='user_detail'),
    path('users/<uuid:user_id>/edit/', views.user_edit, name='user_edit'),
    path('users/<uuid:user_id>/toggle-status/', views.user_toggle_status, name='user_toggle_status'),

    # Loans
    path('loans/', views.loan_list, name='loan_list'),
    path('loans/create/', views.loan_create, name='loan_create'),
    path('loans/<uuid:loan_id>/return/', views.loan_return, name='loan_return'),

    # Reservations
    path('books/<uuid:book_id>/reserve/', views.reservation_create, name='reservation_create'),
    path('reservations/<uuid:reservation_id>/cancel/', views.reservation_cancel, name='reservation_cancel'),

    # Reports
    path('reports/', views.reports_dashboard, name='reports_dashboard'),
    path('reports/loans/export/', views.export_loans_report, name='export_loans_report'),
    path('reports/books/export/', views.export_books_report, name='export_books_report'),

    # Search
    path('search/', views.advanced_search, name='advanced_search'),
]
