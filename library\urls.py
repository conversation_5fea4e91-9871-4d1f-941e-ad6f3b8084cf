from django.urls import path
from . import views

urlpatterns = [
    # Authentication
    path('login/', views.custom_login, name='login'),
    path('logout/', views.custom_logout, name='logout'),
    path('register/', views.register_user, name='register'),
    path('profile/', views.profile, name='profile'),

    # Home
    path('', views.home, name='home'),
    path('student-dashboard/', views.student_dashboard, name='student_dashboard'),
    path('request-book/<uuid:book_id>/', views.request_book, name='request_book'),
    path('pending-requests/', views.pending_requests, name='pending_requests'),
    path('approve-request/<uuid:loan_id>/', views.approve_request, name='approve_request'),
    path('reject-request/<uuid:loan_id>/', views.reject_request, name='reject_request'),
    path('cancel-request/<uuid:loan_id>/', views.cancel_request, name='cancel_request'),

    # Books
    path('books/', views.book_list, name='book_list'),
    path('books/<uuid:book_id>/', views.book_detail, name='book_detail'),
    path('books/add/', views.book_add, name='book_add'),
    path('books/<uuid:book_id>/edit/', views.book_edit, name='book_edit'),
    path('books/<uuid:book_id>/delete/', views.book_delete, name='book_delete'),

    # Users (Admin only)
    path('users/', views.user_list, name='user_list'),
    path('users/<uuid:user_id>/', views.user_detail, name='user_detail'),
    path('users/<uuid:user_id>/edit/', views.user_edit, name='user_edit'),
    path('users/<uuid:user_id>/toggle-status/', views.user_toggle_status, name='user_toggle_status'),

    # Loans
    path('loans/', views.loan_list, name='loan_list'),
    path('loans/create/', views.loan_create, name='loan_create'),
    path('loans/<uuid:loan_id>/return/', views.loan_return, name='loan_return'),

    # Reservations
    path('books/<uuid:book_id>/reserve/', views.reservation_create, name='reservation_create'),
    path('reservations/<uuid:reservation_id>/cancel/', views.reservation_cancel, name='reservation_cancel'),

    # Reports
    path('reports/', views.reports_dashboard, name='reports_dashboard'),
    path('reports/loans/export/', views.export_loans_report, name='export_loans_report'),
    path('reports/books/export/', views.export_books_report, name='export_books_report'),

    # Search
    path('search/', views.advanced_search, name='advanced_search'),

    # User Management
    path('user-management/', views.user_management, name='user_management'),
    path('csv-template/', views.download_csv_template, name='download_csv_template'),
    path('import-csv/', views.import_users_csv, name='import_users_csv'),
    path('sync-api/', views.sync_school_api, name='sync_school_api'),

    # Settings
    path('settings/', views.library_settings, name='library_settings'),

    # Management pages
    path('authors/', views.author_list, name='author_list'),
    path('authors/add/', views.author_add, name='author_add'),
    path('authors/<int:author_id>/edit/', views.author_edit, name='author_edit'),
    path('authors/<int:author_id>/delete/', views.author_delete, name='author_delete'),

    path('categories/', views.category_list, name='category_list'),
    path('categories/add/', views.category_add, name='category_add'),
    path('categories/<int:category_id>/edit/', views.category_edit, name='category_edit'),
    path('categories/<int:category_id>/delete/', views.category_delete, name='category_delete'),

    path('publishers/', views.publisher_list, name='publisher_list'),
    path('publishers/add/', views.publisher_add, name='publisher_add'),
    path('publishers/<int:publisher_id>/edit/', views.publisher_edit, name='publisher_edit'),
    path('publishers/<int:publisher_id>/delete/', views.publisher_delete, name='publisher_delete'),

    path('sections/', views.section_list, name='section_list'),
    path('sections/add/', views.section_add, name='section_add'),
    path('sections/<int:section_id>/edit/', views.section_edit, name='section_edit'),
    path('sections/<int:section_id>/delete/', views.section_delete, name='section_delete'),

    path('shelf-locations/', views.shelf_location_list, name='shelf_location_list'),
    path('shelf-locations/add/', views.shelf_location_add, name='shelf_location_add'),
    path('shelf-locations/<int:shelf_id>/edit/', views.shelf_location_edit, name='shelf_location_edit'),
    path('shelf-locations/<int:shelf_id>/delete/', views.shelf_location_delete, name='shelf_location_delete'),

    path('floors/', views.floor_list, name='floor_list'),
    path('floors/add/', views.floor_add, name='floor_add'),
    path('floors/<int:floor_id>/edit/', views.floor_edit, name='floor_edit'),
    path('floors/<int:floor_id>/delete/', views.floor_delete, name='floor_delete'),

    # API endpoints for autocomplete
    path('api/publishers/', views.publisher_autocomplete, name='publisher_autocomplete'),
    path('api/categories/', views.category_autocomplete, name='category_autocomplete'),
    path('api/sections/', views.section_autocomplete, name='section_autocomplete'),
    path('api/shelf-locations/', views.shelf_location_autocomplete, name='shelf_location_autocomplete'),
    path('api/floors/', views.floor_autocomplete, name='floor_autocomplete'),
    path('api/books/', views.book_autocomplete, name='book_autocomplete'),
    path('api/users/', views.user_autocomplete, name='user_autocomplete'),
    path('api/notifications/', views.notifications_api, name='notifications_api'),
    path('api/notifications/<int:notification_id>/read/', views.mark_notification_read, name='mark_notification_read'),
    path('api/notifications/mark-all-read/', views.mark_all_notifications_read, name='mark_all_notifications_read'),
]
