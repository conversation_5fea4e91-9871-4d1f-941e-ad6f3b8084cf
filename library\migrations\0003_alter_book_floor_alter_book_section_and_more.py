# Generated by Django 5.2.3 on 2025-06-19 11:06

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('library', '0002_alter_book_publisher'),
    ]

    operations = [
        migrations.AlterField(
            model_name='book',
            name='floor',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='books', to='library.floor'),
        ),
        migrations.AlterField(
            model_name='book',
            name='section',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='books', to='library.section'),
        ),
        migrations.AlterField(
            model_name='book',
            name='shelf_location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='books', to='library.shelflocation'),
        ),
    ]
