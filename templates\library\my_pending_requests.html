{% extends 'base.html' %}
{% load static %}

{% block title %}My Pending Requests - Library Management System{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">My Pending Requests</h1>
                <p class="text-gray-600 mt-2">Track your book borrowing requests awaiting approval</p>
            </div>
            <div class="flex space-x-4">
                <a href="{% url 'book_list' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-sm">
                    <i class="fas fa-search mr-2"></i>Browse Books
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-orange-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Pending Requests</p>
                    <p class="text-2xl font-bold text-gray-900">{{ pending_requests.count }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calendar text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Avg. Wait Time</p>
                    <p class="text-2xl font-bold text-gray-900">1-2 days</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Approval Rate</p>
                    <p class="text-2xl font-bold text-gray-900">95%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Requests List -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Pending Requests</h2>
        </div>
        
        {% if pending_requests %}
        <div class="p-6">
            <div class="space-y-4">
                {% for request in pending_requests %}
                <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow duration-200">
                    <div class="flex items-start justify-between">
                        <div class="flex items-start space-x-4">
                            {% if request.book.cover_image %}
                            <img src="{{ request.book.cover_image.url }}" alt="{{ request.book.title }}" class="w-16 h-20 object-cover rounded-lg">
                            {% else %}
                            <div class="w-16 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                                <i class="fas fa-book text-gray-400"></i>
                            </div>
                            {% endif %}
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900">{{ request.book.title }}</h3>
                                {% if request.book.subtitle %}
                                <p class="text-gray-600">{{ request.book.subtitle }}</p>
                                {% endif %}
                                <p class="text-sm text-gray-500 mt-1">{{ request.book.get_authors_display }}</p>
                                <div class="mt-3 grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="font-medium text-gray-700">Requested:</span>
                                        <span class="text-gray-600">{{ request.issue_date|date:"M d, Y" }}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-700">Expected Due:</span>
                                        <span class="text-gray-600">{{ request.due_date|date:"M d, Y" }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col items-end space-y-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                <i class="fas fa-clock mr-1"></i>Awaiting Approval
                            </span>
                            <a href="{% url 'cancel_request' request.id %}" class="text-red-600 hover:text-red-800 text-sm font-medium">
                                Cancel Request
                            </a>
                        </div>
                    </div>
                    
                    <!-- Additional Info -->
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <div class="flex items-center justify-between text-sm text-gray-600">
                            <div class="flex items-center space-x-4">
                                <span>
                                    <i class="fas fa-book mr-1"></i>
                                    ISBN: {{ request.book.isbn }}
                                </span>
                                <span>
                                    <i class="fas fa-building mr-1"></i>
                                    {{ request.book.publisher|default:"Unknown Publisher" }}
                                </span>
                            </div>
                            <div class="text-xs text-gray-500">
                                Request ID: {{ request.id|slice:":8" }}...
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% else %}
        <div class="text-center py-12">
            <i class="fas fa-clock text-gray-300 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Pending Requests</h3>
            <p class="text-gray-500 mb-6">You don't have any pending book requests at the moment.</p>
            <a href="{% url 'book_list' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200">
                Browse Books
            </a>
        </div>
        {% endif %}
    </div>

    <!-- Help Section -->
    <div class="mt-8 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-question-circle mr-2 text-gray-600"></i>
            Request Process
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="font-medium text-gray-900 mb-2">How It Works</h4>
                <ol class="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                    <li>Submit your book borrowing request</li>
                    <li>Librarian reviews your request</li>
                    <li>Get notified of approval/rejection</li>
                    <li>Visit library to collect approved books</li>
                </ol>
            </div>
            <div>
                <h4 class="font-medium text-gray-900 mb-2">Important Notes</h4>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>• Requests are usually processed within 1-2 days</li>
                    <li>• You can cancel requests anytime before approval</li>
                    <li>• Approved books must be collected within 3 days</li>
                    <li>• Check your notifications for updates</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
