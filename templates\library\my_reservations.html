{% extends 'base.html' %}
{% load static %}

{% block title %}My Reservations - Library Management System{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">My Reservations</h1>
                <p class="text-gray-600 mt-2">Track your book reservations and queue position</p>
            </div>
            <div class="flex space-x-4">
                <a href="{% url 'book_list' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-sm">
                    <i class="fas fa-search mr-2"></i>Browse Books
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-bookmark text-purple-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Active Reservations</p>
                    <p class="text-2xl font-bold text-gray-900">{{ reservations.count }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Ready to Collect</p>
                    <p class="text-2xl font-bold text-gray-900">0</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-history text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Reserved</p>
                    <p class="text-2xl font-bold text-gray-900">{{ reservations.count }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Reservations List -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">My Reservations</h2>
        </div>
        
        {% if reservations %}
        <div class="p-6">
            <div class="space-y-4">
                {% for reservation in reservations %}
                <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow duration-200">
                    <div class="flex items-start justify-between">
                        <div class="flex items-start space-x-4">
                            {% if reservation.book.cover_image %}
                            <img src="{{ reservation.book.cover_image.url }}" alt="{{ reservation.book.title }}" class="w-16 h-20 object-cover rounded-lg">
                            {% else %}
                            <div class="w-16 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                                <i class="fas fa-book text-gray-400"></i>
                            </div>
                            {% endif %}
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900">{{ reservation.book.title }}</h3>
                                {% if reservation.book.subtitle %}
                                <p class="text-gray-600">{{ reservation.book.subtitle }}</p>
                                {% endif %}
                                <p class="text-sm text-gray-500 mt-1">{{ reservation.book.get_authors_display }}</p>
                                <div class="mt-3 grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="font-medium text-gray-700">Reserved:</span>
                                        <span class="text-gray-600">{{ reservation.reservation_date|date:"M d, Y" }}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-700">Queue Position:</span>
                                        <span class="text-purple-600 font-medium">#{{ reservation.priority }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col items-end space-y-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                <i class="fas fa-bookmark mr-1"></i>Reserved
                            </span>
                            <a href="{% url 'reservation_cancel' reservation.id %}" class="text-red-600 hover:text-red-800 text-sm font-medium">
                                Cancel Reservation
                            </a>
                        </div>
                    </div>
                    
                    <!-- Additional Info -->
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <div class="flex items-center justify-between text-sm text-gray-600">
                            <div class="flex items-center space-x-4">
                                <span>
                                    <i class="fas fa-users mr-1"></i>
                                    {{ reservation.book.total_copies }} total copies
                                </span>
                                <span>
                                    <i class="fas fa-hand-holding mr-1"></i>
                                    {{ reservation.book.available_copies }} available
                                </span>
                            </div>
                            {% if reservation.priority == 1 and reservation.book.available_copies > 0 %}
                            <span class="text-green-600 font-medium">
                                <i class="fas fa-bell mr-1"></i>Ready to collect!
                            </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% else %}
        <div class="text-center py-12">
            <i class="fas fa-bookmark text-gray-300 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Active Reservations</h3>
            <p class="text-gray-500 mb-6">You don't have any book reservations at the moment.</p>
            <a href="{% url 'book_list' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200">
                Browse Books
            </a>
        </div>
        {% endif %}
    </div>

    <!-- Help Section -->
    <div class="mt-8 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-question-circle mr-2 text-gray-600"></i>
            How Reservations Work
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="font-medium text-gray-900 mb-2">Reservation Process</h4>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>• Reserve books that are currently unavailable</li>
                    <li>• You'll be added to the queue in order</li>
                    <li>• Get notified when your book becomes available</li>
                    <li>• You have 48 hours to collect the book</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium text-gray-900 mb-2">Important Notes</h4>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>• Reservations are free of charge</li>
                    <li>• You can cancel anytime before collection</li>
                    <li>• Priority is based on reservation date</li>
                    <li>• Maximum 3 active reservations per user</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
