{% extends 'base.html' %}
{% load library_extras %}

{% block title %}{{ title }} - LibraryPro{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ title }}</h1>
            <p class="mt-1 text-sm text-gray-500">
                {% if book %}Update book information{% else %}Add a new book to the library collection{% endif %}
            </p>
        </div>
        <a href="{% if book %}{% url 'book_detail' book.id %}{% else %}{% url 'book_list' %}{% endif %}" 
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            <i class="fas fa-arrow-left mr-2"></i>
            Back
        </a>
    </div>

    <form method="post" enctype="multipart/form-data" class="space-y-6">
        {% csrf_token %}
        
        <div class="bg-white shadow-xl rounded-2xl overflow-hidden">
            <!-- Basic Information -->
            <div class="px-8 py-6 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-book text-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900">Basic Information</h3>
                        <p class="text-sm text-gray-600">Essential book details</p>
                    </div>
                </div>
            </div>
            <div class="px-8 py-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Title <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-book text-gray-400"></i>
                            </div>
                            <input type="text" name="title" value="{{ form.title.value|default:'' }}"
                                   class="pl-10 block w-full border-2 border-gray-300 rounded-lg bg-white shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 px-4 py-3"
                                   placeholder="Enter book title" required>
                        </div>
                        {% if form.title.errors %}
                        <p class="mt-2 text-sm text-red-600 flex items-center">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.title.errors.0 }}
                        </p>
                        {% endif %}
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Subtitle</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-heading text-gray-400"></i>
                            </div>
                            <input type="text" name="subtitle" value="{{ form.subtitle.value|default:'' }}"
                                   class="pl-10 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                   placeholder="Enter book subtitle (optional)">
                        </div>
                        {% if form.subtitle.errors %}
                        <p class="mt-2 text-sm text-red-600 flex items-center">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.subtitle.errors.0 }}
                        </p>
                        {% endif %}
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            ISBN <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-barcode text-gray-400"></i>
                            </div>
                            <input type="text" name="isbn" value="{{ form.isbn.value|default:'' }}"
                                   class="pl-10 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                   placeholder="978-0-123456-78-9" required>
                        </div>
                        {% if form.isbn.errors %}
                        <p class="mt-2 text-sm text-red-600 flex items-center">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.isbn.errors.0 }}
                        </p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Publisher</label>
                        <select name="publisher" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="">Select Publisher</option>
                            {% for publisher in form.publisher.queryset %}
                            <option value="{{ publisher.id }}" {% if form.publisher.value == publisher.id %}selected{% endif %}>
                                {{ publisher.name }}
                            </option>
                            {% endfor %}
                        </select>
                        {% if form.publisher.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.publisher.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Authors and Categories -->
            <div class="px-6 py-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Authors and Categories</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Authors <span class="text-red-500">*</span>
                        </label>
                        <div class="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
                            {% for author in form.authors.queryset %}
                            <label class="flex items-center space-x-2 py-1">
                                <input type="checkbox" name="authors" value="{{ author.id }}" 
                                       {% if author.id in form.authors.value %}checked{% endif %}
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="text-sm text-gray-700">{{ author.get_full_name }}</span>
                            </label>
                            {% endfor %}
                        </div>
                        {% if form.authors.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.authors.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Categories <span class="text-red-500">*</span>
                        </label>
                        <div class="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
                            {% for category in form.categories.queryset %}
                            <label class="flex items-center space-x-2 py-1">
                                <input type="checkbox" name="categories" value="{{ category.id }}" 
                                       {% if category.id in form.categories.value %}checked{% endif %}
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="text-sm text-gray-700">{{ category.name }}</span>
                            </label>
                            {% endfor %}
                        </div>
                        {% if form.categories.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.categories.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Publication Details -->
            <div class="px-6 py-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Publication Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Publication Date <span class="text-red-500">*</span>
                        </label>
                        <input type="date" name="publication_date" value="{{ form.publication_date.value|default:'' }}"
                               class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                        {% if form.publication_date.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.publication_date.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Edition</label>
                        <input type="text" name="edition" value="{{ form.edition.value|default:'' }}"
                               class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                               placeholder="1st, 2nd, etc.">
                        {% if form.edition.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.edition.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Pages <span class="text-red-500">*</span>
                        </label>
                        <input type="number" name="pages" value="{{ form.pages.value|default:'' }}" min="1"
                               class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                               placeholder="Number of pages" required>
                        {% if form.pages.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.pages.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Language</label>
                        <select name="language" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="English" {% if form.language.value == 'English' %}selected{% endif %}>English</option>
                            <option value="Spanish" {% if form.language.value == 'Spanish' %}selected{% endif %}>Spanish</option>
                            <option value="French" {% if form.language.value == 'French' %}selected{% endif %}>French</option>
                            <option value="German" {% if form.language.value == 'German' %}selected{% endif %}>German</option>
                            <option value="Other" {% if form.language.value == 'Other' %}selected{% endif %}>Other</option>
                        </select>
                        {% if form.language.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.language.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Price</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">$</span>
                            </div>
                            <input type="number" name="price" value="{{ form.price.value|default:'' }}" step="0.01" min="0"
                                   class="block w-full pl-7 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="0.00">
                        </div>
                        {% if form.price.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.price.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Location and Copies -->
            <div class="px-6 py-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Location and Availability</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Section <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="section" value="{{ form.section.value|default:'' }}"
                               class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                               placeholder="Fiction, Science, etc." required>
                        {% if form.section.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.section.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Shelf Location <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="shelf_location" value="{{ form.shelf_location.value|default:'' }}"
                               class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                               placeholder="A1, B2, etc." required>
                        {% if form.shelf_location.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.shelf_location.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Floor</label>
                        <select name="floor" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="">Select Floor</option>
                            <option value="Ground Floor" {% if form.floor.value == 'Ground Floor' %}selected{% endif %}>Ground Floor</option>
                            <option value="First Floor" {% if form.floor.value == 'First Floor' %}selected{% endif %}>First Floor</option>
                            <option value="Second Floor" {% if form.floor.value == 'Second Floor' %}selected{% endif %}>Second Floor</option>
                            <option value="Basement" {% if form.floor.value == 'Basement' %}selected{% endif %}>Basement</option>
                        </select>
                        {% if form.floor.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.floor.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Total Copies <span class="text-red-500">*</span>
                        </label>
                        <input type="number" name="total_copies" value="{{ form.total_copies.value|default:'1' }}" min="1"
                               class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                        {% if form.total_copies.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.total_copies.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="px-6 py-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Cover Image</label>
                        <input type="file" name="cover_image" accept="image/*"
                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                        {% if form.cover_image.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.cover_image.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Summary</label>
                        <textarea name="summary" rows="4"
                                  class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                  placeholder="Brief description of the book...">{{ form.summary.value|default:'' }}</textarea>
                        {% if form.summary.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.summary.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Internal Notes</label>
                        <textarea name="notes" rows="3"
                                  class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                  placeholder="Internal notes for library staff...">{{ form.notes.value|default:'' }}</textarea>
                        {% if form.notes.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.notes.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
                <a href="{% if book %}{% url 'book_detail' book.id %}{% else %}{% url 'book_list' %}{% endif %}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" 
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-save mr-2"></i>
                    {% if book %}Update Book{% else %}Add Book{% endif %}
                </button>
            </div>
        </div>
    </form>
</div>
{% endblock %}
