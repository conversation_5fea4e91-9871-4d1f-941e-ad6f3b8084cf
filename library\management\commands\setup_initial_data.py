from django.core.management.base import BaseCommand
from django.contrib.auth.hashers import make_password
from library.models import User, Publisher, Category, Author, Section, ShelfLocation, Floor


class Command(BaseCommand):
    help = 'Set up initial data for the library system'

    def handle(self, *args, **options):
        self.stdout.write('Setting up initial data...')

        # Create admin user if not exists
        if not User.objects.filter(username='admin').exists():
            admin_user = User.objects.create(
                username='admin',
                email='<EMAIL>',
                first_name='Library',
                last_name='Admin',
                role='admin',
                is_staff=True,
                is_superuser=True,
                password=make_password('admin123'),
                is_active_member=True
            )
            self.stdout.write(f'Created admin user: {admin_user.username}')

        # Create sample floors
        floors_data = [
            {'name': 'Ground Floor', 'description': 'Main entrance and reception'},
            {'name': 'First Floor', 'description': 'General collection and study areas'},
            {'name': 'Second Floor', 'description': 'Reference and research materials'},
            {'name': 'Basement', 'description': 'Archives and storage'},
        ]

        for floor_data in floors_data:
            floor, created = Floor.objects.get_or_create(
                name=floor_data['name'],
                defaults={'description': floor_data['description']}
            )
            if created:
                self.stdout.write(f'Created floor: {floor.name}')

        # Create sample sections
        sections_data = [
            {'name': 'Fiction', 'description': 'Novels and fictional works', 'floor': 'First Floor'},
            {'name': 'Non-Fiction', 'description': 'Educational and informational books', 'floor': 'First Floor'},
            {'name': 'Science', 'description': 'Scientific and technical books', 'floor': 'Second Floor'},
            {'name': 'History', 'description': 'Historical books and documents', 'floor': 'Second Floor'},
            {'name': 'Children', 'description': 'Books for children and young adults', 'floor': 'Ground Floor'},
            {'name': 'Reference', 'description': 'Dictionaries, encyclopedias, and reference materials', 'floor': 'Second Floor'},
        ]

        for section_data in sections_data:
            floor = Floor.objects.get(name=section_data['floor'])
            section, created = Section.objects.get_or_create(
                name=section_data['name'],
                defaults={
                    'description': section_data['description'],
                    'floor': floor.name
                }
            )
            if created:
                self.stdout.write(f'Created section: {section.name}')

        # Create sample shelf locations
        shelf_locations_data = [
            {'code': 'A1', 'section': 'Fiction', 'description': 'Fiction A-D', 'capacity': 50},
            {'code': 'A2', 'section': 'Fiction', 'description': 'Fiction E-H', 'capacity': 50},
            {'code': 'A3', 'section': 'Fiction', 'description': 'Fiction I-M', 'capacity': 50},
            {'code': 'B1', 'section': 'Non-Fiction', 'description': 'General Non-Fiction', 'capacity': 60},
            {'code': 'B2', 'section': 'Non-Fiction', 'description': 'Biography and Autobiography', 'capacity': 40},
            {'code': 'C1', 'section': 'Science', 'description': 'Physics and Chemistry', 'capacity': 45},
            {'code': 'C2', 'section': 'Science', 'description': 'Biology and Medicine', 'capacity': 45},
            {'code': 'D1', 'section': 'History', 'description': 'World History', 'capacity': 55},
            {'code': 'E1', 'section': 'Children', 'description': 'Picture Books', 'capacity': 30},
            {'code': 'E2', 'section': 'Children', 'description': 'Young Adult', 'capacity': 35},
            {'code': 'R1', 'section': 'Reference', 'description': 'Dictionaries and Encyclopedias', 'capacity': 25},
        ]

        for shelf_data in shelf_locations_data:
            section = Section.objects.get(name=shelf_data['section'])
            shelf, created = ShelfLocation.objects.get_or_create(
                code=shelf_data['code'],
                defaults={
                    'section': section,
                    'description': shelf_data['description'],
                    'capacity': shelf_data['capacity']
                }
            )
            if created:
                self.stdout.write(f'Created shelf location: {shelf.code}')

        # Create sample publishers
        publishers_data = [
            {'name': 'Penguin Random House', 'address': 'New York, NY', 'email': '<EMAIL>'},
            {'name': 'HarperCollins', 'address': 'New York, NY', 'email': '<EMAIL>'},
            {'name': 'Simon & Schuster', 'address': 'New York, NY', 'email': '<EMAIL>'},
            {'name': 'Macmillan Publishers', 'address': 'London, UK', 'email': '<EMAIL>'},
            {'name': 'Scholastic', 'address': 'New York, NY', 'email': '<EMAIL>'},
            {'name': 'Oxford University Press', 'address': 'Oxford, UK', 'email': '<EMAIL>'},
            {'name': 'Cambridge University Press', 'address': 'Cambridge, UK', 'email': '<EMAIL>'},
        ]

        for pub_data in publishers_data:
            publisher, created = Publisher.objects.get_or_create(
                name=pub_data['name'],
                defaults={
                    'address': pub_data['address'],
                    'email': pub_data['email']
                }
            )
            if created:
                self.stdout.write(f'Created publisher: {publisher.name}')

        # Create sample categories
        categories_data = [
            {'name': 'Fiction', 'description': 'Fictional literature including novels and short stories'},
            {'name': 'Mystery', 'description': 'Mystery and detective fiction'},
            {'name': 'Romance', 'description': 'Romantic fiction'},
            {'name': 'Science Fiction', 'description': 'Science fiction and fantasy'},
            {'name': 'Biography', 'description': 'Biographical works'},
            {'name': 'History', 'description': 'Historical books and documents'},
            {'name': 'Science', 'description': 'Scientific and technical books'},
            {'name': 'Mathematics', 'description': 'Mathematics and statistics'},
            {'name': 'Computer Science', 'description': 'Programming and computer science'},
            {'name': 'Children', 'description': 'Books for children'},
            {'name': 'Young Adult', 'description': 'Books for teenagers and young adults'},
            {'name': 'Reference', 'description': 'Reference materials'},
        ]

        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            if created:
                self.stdout.write(f'Created category: {category.name}')

        # Create sample authors
        authors_data = [
            {'first_name': 'J.K.', 'last_name': 'Rowling', 'nationality': 'British'},
            {'first_name': 'Stephen', 'last_name': 'King', 'nationality': 'American'},
            {'first_name': 'Agatha', 'last_name': 'Christie', 'nationality': 'British'},
            {'first_name': 'George', 'last_name': 'Orwell', 'nationality': 'British'},
            {'first_name': 'Jane', 'last_name': 'Austen', 'nationality': 'British'},
            {'first_name': 'Mark', 'last_name': 'Twain', 'nationality': 'American'},
            {'first_name': 'Charles', 'last_name': 'Dickens', 'nationality': 'British'},
            {'first_name': 'William', 'last_name': 'Shakespeare', 'nationality': 'British'},
        ]

        for author_data in authors_data:
            author, created = Author.objects.get_or_create(
                first_name=author_data['first_name'],
                last_name=author_data['last_name'],
                defaults={'nationality': author_data['nationality']}
            )
            if created:
                self.stdout.write(f'Created author: {author.get_full_name()}')

        self.stdout.write(self.style.SUCCESS('Initial data setup completed successfully!'))
        self.stdout.write('Admin credentials: username=admin, password=admin123')
