{% extends 'base.html' %}

{% block title %}Delete Book - LibraryPro{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto animate-fade-in">
    <!-- Header -->
    <div class="mb-8 text-center">
        <h1 class="text-4xl font-bold text-gray-800 mb-2">Delete Book</h1>
        <p class="text-gray-600">Permanently remove this book from the library</p>
    </div>

    <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
        <!-- Warning Header -->
        <div class="bg-gradient-to-r from-red-50 to-orange-50 px-8 py-6 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                <i class="fas fa-exclamation-triangle text-red-600 mr-3"></i>
                Confirm Book Deletion
            </h2>
        </div>

        <!-- Book Details -->
        <div class="p-8">
            <div class="bg-gray-50 rounded-2xl p-6 mb-8">
                <div class="flex items-start space-x-6">
                    <!-- Book Cover -->
                    <div class="flex-shrink-0">
                        <div class="w-24 h-32 bg-gray-200 rounded-lg flex items-center justify-center">
                            {% if book.cover_image %}
                            <img src="{{ book.cover_image.url }}" alt="{{ book.title }}" class="w-24 h-32 object-cover rounded-lg">
                            {% else %}
                            <i class="fas fa-book text-3xl text-gray-400"></i>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Book Info -->
                    <div class="flex-1">
                        <h3 class="text-2xl font-bold text-gray-800 mb-3">{{ book.title }}</h3>
                        {% if book.subtitle %}
                        <h4 class="text-lg text-gray-600 mb-3">{{ book.subtitle }}</h4>
                        {% endif %}
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                            <div class="flex items-center">
                                <i class="fas fa-user text-gray-400 w-4 mr-3"></i>
                                <span class="text-gray-600">Author(s): <strong>{{ book.get_authors_display }}</strong></span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-hashtag text-gray-400 w-4 mr-3"></i>
                                <span class="text-gray-600">ISBN: <strong>{{ book.isbn }}</strong></span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-building text-gray-400 w-4 mr-3"></i>
                                <span class="text-gray-600">Publisher: <strong>{{ book.publisher|default:"N/A" }}</strong></span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-calendar text-gray-400 w-4 mr-3"></i>
                                <span class="text-gray-600">Published: <strong>{{ book.publication_date|date:"Y" }}</strong></span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-map-marker-alt text-gray-400 w-4 mr-3"></i>
                                <span class="text-gray-600">Location: <strong>{{ book.section }} - {{ book.shelf_location }}</strong></span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-copy text-gray-400 w-4 mr-3"></i>
                                <span class="text-gray-600">Copies: <strong>{{ book.available_copies }}/{{ book.total_copies }}</strong></span>
                            </div>
                        </div>
                        
                        <!-- Categories -->
                        <div class="mt-4">
                            <div class="flex flex-wrap gap-2">
                                {% for category in book.categories.all %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ category.name }}
                                </span>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Warning Message -->
            <div class="bg-red-50 border border-red-200 rounded-xl p-6 mb-8">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="font-semibold text-red-800 mb-2">Warning: This action cannot be undone!</h4>
                        <p class="text-red-700 text-sm mb-3">
                            Deleting this book will permanently remove it from the library system. This will affect:
                        </p>
                        <ul class="text-sm text-red-700 space-y-1">
                            <li class="flex items-center">
                                <i class="fas fa-times text-red-500 mr-2"></i>
                                All book information and metadata will be lost
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-times text-red-500 mr-2"></i>
                                Historical loan records will reference a deleted book
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-times text-red-500 mr-2"></i>
                                Any active reservations will be cancelled
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-times text-red-500 mr-2"></i>
                                The book cannot be recovered after deletion
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Active Loans Check -->
            {% if book.loans.filter.status='active' %}
            <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-8">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-ban text-2xl text-yellow-600"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="font-semibold text-yellow-800 mb-2">Cannot Delete: Active Loans</h4>
                        <p class="text-yellow-700 text-sm">
                            This book cannot be deleted because it has active loans. Please ensure all copies are returned before attempting to delete this book.
                        </p>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Confirmation Input -->
            <div class="bg-gray-50 rounded-xl p-6 mb-8">
                <h4 class="font-semibold text-gray-800 mb-3">Type the book title to confirm deletion:</h4>
                <div class="space-y-3">
                    <div class="text-sm text-gray-600">
                        Expected: <code class="bg-gray-200 px-2 py-1 rounded">{{ book.title }}</code>
                    </div>
                    <input type="text" id="confirmTitle" 
                           class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-300"
                           placeholder="Type the exact book title here..."
                           autocomplete="off">
                    <div id="confirmMessage" class="text-sm text-red-600 hidden">
                        <i class="fas fa-times mr-1"></i>
                        Title does not match. Please type the exact title.
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <form method="post" id="deleteForm" class="flex justify-end space-x-4">
                {% csrf_token %}
                <a href="{% url 'book_detail' book.id %}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-8 py-3 rounded-xl font-medium transition-all duration-300">
                    <i class="fas fa-arrow-left mr-2"></i>Cancel
                </a>
                <button type="submit" id="deleteButton" disabled
                        class="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-8 py-3 rounded-xl font-medium transition-all duration-300 shadow-lg">
                    <i class="fas fa-trash mr-2"></i>Delete Book
                </button>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmInput = document.getElementById('confirmTitle');
    const deleteButton = document.getElementById('deleteButton');
    const confirmMessage = document.getElementById('confirmMessage');
    const expectedTitle = "{{ book.title|escapejs }}";
    const deleteForm = document.getElementById('deleteForm');
    
    confirmInput.addEventListener('input', function() {
        const inputValue = this.value.trim();
        
        if (inputValue === expectedTitle) {
            deleteButton.disabled = false;
            deleteButton.classList.remove('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
            deleteButton.classList.add('bg-red-600', 'hover:bg-red-700');
            confirmMessage.classList.add('hidden');
        } else {
            deleteButton.disabled = true;
            deleteButton.classList.add('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
            deleteButton.classList.remove('bg-red-600', 'hover:bg-red-700');
            
            if (inputValue.length > 0) {
                confirmMessage.classList.remove('hidden');
            } else {
                confirmMessage.classList.add('hidden');
            }
        }
    });
    
    deleteForm.addEventListener('submit', function(e) {
        if (confirmInput.value.trim() !== expectedTitle) {
            e.preventDefault();
            alert('Please type the exact book title to confirm deletion.');
            return false;
        }
        
        if (!confirm('Are you absolutely sure you want to delete this book? This action cannot be undone.')) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}
