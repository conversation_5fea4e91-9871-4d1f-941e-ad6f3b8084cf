# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2016,2020-2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-02-01 22:11+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Lower Sorbian (http://www.transifex.com/django/django/"
"language/dsb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: dsb\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n"
"%100==4 ? 2 : 3);\n"

msgid "Redirects"
msgstr "Dalejpósrědnjenja"

msgid "site"
msgstr "sedło"

msgid "redirect from"
msgstr "dalejpósrědnjenje wót"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"To by dejało absolutna sćažka byś, mimo domenowego mjenja. Pśikład: „/events/"
"search/“."

msgid "redirect to"
msgstr "dalejpósrědnjenje do"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"To móžo pak absolutna sćažka byś (ako górjejce) pak dopołny URL, kótaryž "
"zachopina se z „https://“."

msgid "redirect"
msgstr "dalejpósrědnjenje"

msgid "redirects"
msgstr "dalejpósrědnjenja"
