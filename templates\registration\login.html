<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Library Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gradient-to-br from-blue-500 to-purple-600 min-h-screen flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-md">
        <div class="text-center mb-8">
            <i class="fas fa-book text-4xl text-blue-600 mb-4"></i>
            <h1 class="text-2xl font-bold text-gray-800">Library Management System</h1>
            <p class="text-gray-600 mt-2">Please sign in to your account</p>
        </div>

        {% if messages %}
        {% for message in messages %}
        <div class="mb-4 p-4 rounded-md {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'warning' %}bg-yellow-100 border border-yellow-400 text-yellow-700{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
        {% endif %}

        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div>
                <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-user mr-1"></i>Username or Email
                </label>
                {{ form.username }}
                {% if form.username.errors %}
                <div class="mt-1 text-sm text-red-600">
                    {{ form.username.errors.0 }}
                </div>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.password.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-lock mr-1"></i>Password
                </label>
                {{ form.password }}
                {% if form.password.errors %}
                <div class="mt-1 text-sm text-red-600">
                    {{ form.password.errors.0 }}
                </div>
                {% endif %}
            </div>

            {% if form.non_field_errors %}
            <div class="text-sm text-red-600">
                {{ form.non_field_errors.0 }}
            </div>
            {% endif %}

            <div>
                <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In
                </button>
            </div>
        </form>

        <div class="mt-6 text-center">
            <p class="text-sm text-gray-600">
                Need an account? Contact your administrator.
            </p>
        </div>
    </div>
</body>
</html>
