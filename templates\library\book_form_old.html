{% extends 'base.html' %}
{% load library_extras %}

{% block title %}{{ title }} - LibraryPro{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ title }}</h1>
            <p class="mt-1 text-sm text-gray-500">
                {% if book %}Update book information{% else %}Add a new book to the library collection{% endif %}
            </p>
        </div>
        <a href="{% if book %}{% url 'book_detail' book.id %}{% else %}{% url 'book_list' %}{% endif %}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            <i class="fas fa-arrow-left mr-2"></i>
            Back
        </a>
    </div>

    <div class="bg-white shadow rounded-lg">
        <form method="post" enctype="multipart/form-data" class="divide-y divide-gray-200">
            {% csrf_token %}
            
            <!-- Basic Information -->
            <div class="px-6 py-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="md:col-span-2">
                        <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Title <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="{{ form.title.name }}" id="{{ form.title.id_for_label }}"
                               value="{{ form.title.value|default:'' }}"
                               class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                               placeholder="Enter book title" required>
                        {% if form.title.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.title.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div class="md:col-span-2">
                        <label for="{{ form.subtitle.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Subtitle</label>
                        {{ form.subtitle }}
                        {% if form.subtitle.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.subtitle.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.isbn.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">ISBN *</label>
                        {{ form.isbn }}
                        {% if form.isbn.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.isbn.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.publisher.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Publisher</label>
                        {{ form.publisher }}
                        {% if form.publisher.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.publisher.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Authors and Categories -->
            <div class="border-b border-gray-200 pb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Authors and Categories</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Authors *</label>
                        <div class="max-h-40 overflow-y-auto border border-gray-300 rounded p-3 space-y-2">
                            {{ form.authors }}
                        </div>
                        {% if form.authors.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.authors.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Categories *</label>
                        <div class="max-h-40 overflow-y-auto border border-gray-300 rounded p-3 space-y-2">
                            {{ form.categories }}
                        </div>
                        {% if form.categories.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.categories.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Publication Details -->
            <div class="border-b border-gray-200 pb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Publication Details</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="{{ form.publication_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Publication Date *</label>
                        {{ form.publication_date }}
                        {% if form.publication_date.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.publication_date.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.edition.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Edition</label>
                        {{ form.edition }}
                        {% if form.edition.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.edition.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.pages.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Pages *</label>
                        {{ form.pages }}
                        {% if form.pages.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.pages.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.language.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Language</label>
                        {{ form.language }}
                        {% if form.language.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.language.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.price.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Price</label>
                        {{ form.price }}
                        {% if form.price.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.price.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Location and Availability -->
            <div class="border-b border-gray-200 pb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Location and Availability</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="{{ form.section.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Section *</label>
                        {{ form.section }}
                        {% if form.section.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.section.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.shelf_location.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Shelf Location *</label>
                        {{ form.shelf_location }}
                        {% if form.shelf_location.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.shelf_location.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.floor.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Floor</label>
                        {{ form.floor }}
                        {% if form.floor.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.floor.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.total_copies.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Total Copies *</label>
                        {{ form.total_copies }}
                        {% if form.total_copies.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.total_copies.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Additional Information -->
            <div class="border-b border-gray-200 pb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Additional Information</h2>
                <div class="space-y-4">
                    <div>
                        <label for="{{ form.cover_image.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Cover Image</label>
                        {{ form.cover_image }}
                        {% if form.cover_image.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.cover_image.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.physical_description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Physical Description</label>
                        {{ form.physical_description }}
                        {% if form.physical_description.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.physical_description.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.summary.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Summary</label>
                        {{ form.summary }}
                        {% if form.summary.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.summary.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Internal Notes</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.notes.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="flex justify-end space-x-4">
                <a href="{% if book %}{% url 'book_detail' book.id %}{% else %}{% url 'book_list' %}{% endif %}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg">
                    Cancel
                </a>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                    <i class="fas fa-save mr-2"></i>Save Book
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
