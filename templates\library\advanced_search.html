{% extends 'base.html' %}

{% block title %}Advanced Search - LibraryPro{% endblock %}

{% block content %}
<div class="space-y-8 animate-fade-in">
    <!-- Header -->
    <div class="text-center">
        <h1 class="text-4xl font-bold text-gray-800 mb-2">Advanced Search</h1>
        <p class="text-gray-600">Find books, users, and loans across the library system</p>
    </div>

    <!-- Search Form -->
    <div class="bg-white rounded-2xl shadow-xl p-8">
        <form method="get" class="space-y-6">
            <!-- Search Type Tabs -->
            <div class="flex flex-wrap justify-center space-x-1 bg-gray-100 rounded-xl p-1">
                <button type="button" onclick="setSearchType('books')" 
                        class="search-tab px-6 py-3 rounded-lg font-medium transition-all duration-300 {% if search_type == 'books' %}bg-white text-blue-600 shadow-md{% else %}text-gray-600 hover:text-gray-800{% endif %}">
                    <i class="fas fa-book mr-2"></i>Books
                </button>
                {% if user.can_manage_users %}
                <button type="button" onclick="setSearchType('users')" 
                        class="search-tab px-6 py-3 rounded-lg font-medium transition-all duration-300 {% if search_type == 'users' %}bg-white text-blue-600 shadow-md{% else %}text-gray-600 hover:text-gray-800{% endif %}">
                    <i class="fas fa-users mr-2"></i>Users
                </button>
                {% endif %}
                {% if user.can_manage_books %}
                <button type="button" onclick="setSearchType('loans')" 
                        class="search-tab px-6 py-3 rounded-lg font-medium transition-all duration-300 {% if search_type == 'loans' %}bg-white text-blue-600 shadow-md{% else %}text-gray-600 hover:text-gray-800{% endif %}">
                    <i class="fas fa-hand-holding mr-2"></i>Loans
                </button>
                {% endif %}
            </div>

            <!-- Search Input -->
            <div class="relative max-w-2xl mx-auto">
                <input type="text" name="q" value="{{ query }}" 
                       placeholder="{% if search_type == 'books' %}Search by title, author, ISBN, publisher...{% elif search_type == 'users' %}Search by name, username, email...{% else %}Search by book title, borrower name...{% endif %}"
                       class="w-full pl-12 pr-20 py-4 text-lg border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 shadow-lg">
                <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-xl"></i>
                <button type="submit" 
                        class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-xl font-medium transition-all duration-300">
                    Search
                </button>
            </div>

            <input type="hidden" name="type" value="{{ search_type }}" id="searchTypeInput">
        </form>
    </div>

    <!-- Search Results -->
    {% if query %}
    <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
        <!-- Results Header -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 px-8 py-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold text-gray-800">
                    Search Results for "{{ query }}"
                </h2>
                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                    {{ total_results }} result{{ total_results|pluralize }}
                </span>
            </div>
        </div>

        <!-- Results Content -->
        <div class="p-8">
            {% if search_type == 'books' %}
                {% if items %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for book in items %}
                    <div class="bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-colors duration-200 card-hover">
                        <div class="flex items-start space-x-4">
                            <div class="w-16 h-20 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                                {% if book.cover_image %}
                                <img src="{{ book.cover_image.url }}" alt="{{ book.title }}" class="w-16 h-20 object-cover rounded-lg">
                                {% else %}
                                <i class="fas fa-book text-gray-400 text-xl"></i>
                                {% endif %}
                            </div>
                            <div class="flex-1 min-w-0">
                                <h3 class="font-semibold text-gray-800 mb-1 line-clamp-2">{{ book.title }}</h3>
                                <p class="text-sm text-gray-600 mb-2">{{ book.get_authors_display }}</p>
                                <div class="flex items-center justify-between">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {% if book.status == 'available' %}bg-green-100 text-green-800
                                        {% elif book.status == 'borrowed' %}bg-yellow-100 text-yellow-800
                                        {% else %}bg-red-100 text-red-800{% endif %}">
                                        {{ book.get_status_display }}
                                    </span>
                                    <a href="{% url 'book_detail' book.id %}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

            {% elif search_type == 'users' %}
                {% if items %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for user_obj in items %}
                    <div class="bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-colors duration-200 card-hover">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
                                {% if user_obj.profile_picture %}
                                <img src="{{ user_obj.profile_picture.url }}" alt="{{ user_obj.get_full_name }}" class="w-12 h-12 rounded-full object-cover">
                                {% else %}
                                <i class="fas fa-user text-gray-400"></i>
                                {% endif %}
                            </div>
                            <div class="flex-1 min-w-0">
                                <h3 class="font-semibold text-gray-800">{{ user_obj.get_full_name }}</h3>
                                <p class="text-sm text-gray-600">{{ user_obj.get_role_display }}</p>
                                <p class="text-xs text-gray-500">{{ user_obj.email }}</p>
                                <div class="mt-2">
                                    <a href="{% url 'user_detail' user_obj.id %}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        View Profile
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

            {% elif search_type == 'loans' %}
                {% if items %}
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Book</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Borrower</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            {% for loan in items %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <div class="font-medium text-gray-900">{{ loan.book.title|truncatechars:40 }}</div>
                                    <div class="text-sm text-gray-500">{{ loan.book.isbn }}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="font-medium text-gray-900">{{ loan.borrower.get_full_name }}</div>
                                    <div class="text-sm text-gray-500">{{ loan.borrower.get_role_display }}</div>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900">{{ loan.issue_date|date:"M d, Y" }}</td>
                                <td class="px-6 py-4 text-sm {% if loan.is_overdue %}text-red-600 font-medium{% else %}text-gray-900{% endif %}">
                                    {{ loan.due_date|date:"M d, Y" }}
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {% if loan.status == 'active' %}bg-blue-100 text-blue-800
                                        {% elif loan.status == 'returned' %}bg-green-100 text-green-800
                                        {% elif loan.status == 'overdue' %}bg-red-100 text-red-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ loan.get_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-sm font-medium">
                                    {% if loan.status == 'active' %}
                                    <a href="{% url 'loan_return' loan.id %}" class="text-green-600 hover:text-green-900">Return</a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% endif %}
            {% endif %}

            {% if not items %}
            <!-- No Results -->
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-search text-4xl text-gray-400"></i>
                </div>
                <h3 class="text-xl font-medium text-gray-500 mb-2">No results found</h3>
                <p class="text-gray-400 mb-6">Try adjusting your search terms or search in a different category.</p>
                <button onclick="document.querySelector('input[name=q]').focus()" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300">
                    Try Another Search
                </button>
            </div>
            {% endif %}
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="bg-gray-50 px-8 py-4 border-t border-gray-200">
            <div class="flex justify-center">
                <nav class="flex space-x-2">
                    {% if page_obj.has_previous %}
                    <a href="?q={{ query }}&type={{ search_type }}&page=1" 
                       class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                        First
                    </a>
                    <a href="?q={{ query }}&type={{ search_type }}&page={{ page_obj.previous_page_number }}" 
                       class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                        Previous
                    </a>
                    {% endif %}

                    <span class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium">
                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>

                    {% if page_obj.has_next %}
                    <a href="?q={{ query }}&type={{ search_type }}&page={{ page_obj.next_page_number }}" 
                       class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                        Next
                    </a>
                    <a href="?q={{ query }}&type={{ search_type }}&page={{ page_obj.paginator.num_pages }}" 
                       class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                        Last
                    </a>
                    {% endif %}
                </nav>
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}
</div>

<script>
function setSearchType(type) {
    document.getElementById('searchTypeInput').value = type;
    
    // Update tab appearance
    document.querySelectorAll('.search-tab').forEach(tab => {
        tab.classList.remove('bg-white', 'text-blue-600', 'shadow-md');
        tab.classList.add('text-gray-600');
    });
    
    event.target.classList.add('bg-white', 'text-blue-600', 'shadow-md');
    event.target.classList.remove('text-gray-600');
    
    // Update placeholder
    const input = document.querySelector('input[name="q"]');
    if (type === 'books') {
        input.placeholder = 'Search by title, author, ISBN, publisher...';
    } else if (type === 'users') {
        input.placeholder = 'Search by name, username, email...';
    } else if (type === 'loans') {
        input.placeholder = 'Search by book title, borrower name...';
    }
}

// Auto-focus search input
document.addEventListener('DOMContentLoaded', function() {
    document.querySelector('input[name="q"]').focus();
});
</script>
{% endblock %}
