{% extends 'base.html' %}
{% load library_extras %}

{% block title %}User Management - LibraryPro{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">User Management</h1>
            <p class="mt-1 text-sm text-gray-500">Add new users individually, import from CSV, or sync with school system</p>
        </div>
        <a href="{% url 'user_list' %}" 
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Users
        </a>
    </div>

    <!-- Tab Navigation -->
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8">
            <button onclick="showTab('single')" id="single-tab" 
                    class="tab-button border-blue-500 text-blue-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                <i class="fas fa-user mr-2"></i>
                Single User
            </button>
            <button onclick="showTab('csv')" id="csv-tab" 
                    class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                <i class="fas fa-file-csv mr-2"></i>
                CSV Import
            </button>
            <button onclick="showTab('api')" id="api-tab" 
                    class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                <i class="fas fa-cloud mr-2"></i>
                School System API
            </button>
        </nav>
    </div>

    <!-- Single User Form -->
    <div id="single-content" class="tab-content">
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Add Individual User</h3>
                <p class="mt-1 text-sm text-gray-500">Create a single user account manually</p>
            </div>
            
            <form method="post" action="{% url 'register' %}" class="divide-y divide-gray-200">
                {% csrf_token %}
                
                <!-- Account Information -->
                <div class="px-6 py-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Account Information</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Username <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="username" required
                                   class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="Enter username">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Email <span class="text-red-500">*</span>
                            </label>
                            <input type="email" name="email" required
                                   class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="<EMAIL>">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Password <span class="text-red-500">*</span>
                            </label>
                            <input type="password" name="password1" required
                                   class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="Enter password">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Confirm Password <span class="text-red-500">*</span>
                            </label>
                            <input type="password" name="password2" required
                                   class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="Confirm password">
                        </div>
                    </div>
                </div>

                <!-- Personal Information -->
                <div class="px-6 py-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Personal Information</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                First Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="first_name" required
                                   class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="First name">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Last Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="last_name" required
                                   class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="Last name">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                            <input type="tel" name="phone_number"
                                   class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="+****************">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                            <input type="date" name="date_of_birth"
                                   class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                            <textarea name="address" rows="3"
                                      class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                      placeholder="Full address"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Role and Academic Information -->
                <div class="px-6 py-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Role & Academic Information</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Role <span class="text-red-500">*</span>
                            </label>
                            <select name="role" id="role-select" required onchange="toggleAcademicFields()"
                                    class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                <option value="">Select Role</option>
                                <option value="student">Student</option>
                                <option value="teacher">Teacher</option>
                                <option value="librarian">Librarian</option>
                                <option value="admin">Administrator</option>
                            </select>
                        </div>
                        
                        <div id="enrollment-field">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Enrollment/ID Number</label>
                            <input type="text" name="enrollment_number"
                                   class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="Student/Staff ID">
                        </div>
                        
                        <div id="class-field">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Class/Grade</label>
                            <input type="text" name="class_grade"
                                   class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="10A, 11B, etc.">
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
                    <button type="button" onclick="resetForm()" 
                            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        Reset
                    </button>
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <i class="fas fa-user-plus mr-2"></i>
                        Create User
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- CSV Import -->
    <div id="csv-content" class="tab-content hidden">
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">CSV Import</h3>
                <p class="mt-1 text-sm text-gray-500">Import multiple users from a CSV file</p>
            </div>
            
            <div class="px-6 py-6">
                <!-- CSV Template Download -->
                <div class="mb-6 p-4 bg-blue-50 rounded-lg">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-400 text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-blue-800">CSV Format Requirements</h4>
                            <div class="mt-2 text-sm text-blue-700">
                                <p>Download the template file and fill in user information. Required columns:</p>
                                <ul class="mt-1 list-disc list-inside">
                                    <li>username, email, first_name, last_name, role</li>
                                    <li>Optional: phone_number, date_of_birth, address, enrollment_number, class_grade</li>
                                </ul>
                            </div>
                            <div class="mt-3">
                                <a href="{% url 'download_csv_template' %}" 
                                   class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200">
                                    <i class="fas fa-download mr-2"></i>
                                    Download CSV Template
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- File Upload -->
                <form method="post" action="{% url 'import_users_csv' %}" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Select CSV File <span class="text-red-500">*</span>
                            </label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                <div class="space-y-1 text-center">
                                    <i class="fas fa-file-csv text-4xl text-gray-400"></i>
                                    <div class="flex text-sm text-gray-600">
                                        <label for="csv-file" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
                                            <span>Upload a file</span>
                                            <input id="csv-file" name="csv_file" type="file" accept=".csv" class="sr-only" required>
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500">CSV files only</p>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center">
                            <input id="send-emails" name="send_welcome_emails" type="checkbox" 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="send-emails" class="ml-2 block text-sm text-gray-900">
                                Send welcome emails to new users
                            </label>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" 
                                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                                <i class="fas fa-upload mr-2"></i>
                                Import Users
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- API Integration -->
    <div id="api-content" class="tab-content hidden">
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">School Management System Integration</h3>
                <p class="mt-1 text-sm text-gray-500">Sync users from your school management system</p>
            </div>
            
            <div class="px-6 py-6">
                <form method="post" action="{% url 'sync_school_api' %}">
                    {% csrf_token %}
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">API Endpoint URL</label>
                                <input type="url" name="api_url" 
                                       class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                       placeholder="https://school-system.com/api/users">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                                <input type="password" name="api_key" 
                                       class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                       placeholder="Enter API key">
                            </div>
                        </div>

                        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-yellow-800">API Integration Requirements</h4>
                                    <div class="mt-2 text-sm text-yellow-700">
                                        <p>Your school management system API should return user data in JSON format with the following fields:</p>
                                        <code class="block mt-2 p-2 bg-yellow-100 rounded text-xs">
                                            {"users": [{"username": "...", "email": "...", "first_name": "...", "last_name": "...", "role": "..."}]}
                                        </code>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-between">
                            <button type="button" onclick="testApiConnection()" 
                                    class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                <i class="fas fa-plug mr-2"></i>
                                Test Connection
                            </button>
                            <button type="submit" 
                                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700">
                                <i class="fas fa-sync mr-2"></i>
                                Sync Users
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Remove active class from all tabs
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show selected tab content
    document.getElementById(tabName + '-content').classList.remove('hidden');
    
    // Add active class to selected tab
    const activeTab = document.getElementById(tabName + '-tab');
    activeTab.classList.remove('border-transparent', 'text-gray-500');
    activeTab.classList.add('border-blue-500', 'text-blue-600');
}

function toggleAcademicFields() {
    const role = document.getElementById('role-select').value;
    const enrollmentField = document.getElementById('enrollment-field');
    const classField = document.getElementById('class-field');
    
    if (role === 'student') {
        enrollmentField.style.display = 'block';
        classField.style.display = 'block';
    } else if (role === 'teacher') {
        enrollmentField.style.display = 'block';
        classField.style.display = 'none';
    } else {
        enrollmentField.style.display = 'none';
        classField.style.display = 'none';
    }
}

function resetForm() {
    document.querySelector('#single-content form').reset();
    toggleAcademicFields();
}

function testApiConnection() {
    // Implement API connection test
    alert('API connection test functionality will be implemented');
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    toggleAcademicFields();
});
</script>
{% endblock %}
