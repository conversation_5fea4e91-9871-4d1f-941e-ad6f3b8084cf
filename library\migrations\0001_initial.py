# Generated by Django 5.2.3 on 2025-06-18 13:03

import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Author',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=100)),
                ('last_name', models.CharField(max_length=100)),
                ('biography', models.TextField(blank=True, null=True)),
                ('birth_date', models.DateField(blank=True, null=True)),
                ('nationality', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['last_name', 'first_name'],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='LibrarySettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('default_loan_period', models.PositiveIntegerField(default=14, help_text='Days')),
                ('max_renewals', models.PositiveIntegerField(default=3)),
                ('max_books_per_user', models.PositiveIntegerField(default=5)),
                ('daily_fine_rate', models.DecimalField(decimal_places=2, default=1.0, max_digits=5)),
                ('max_fine_amount', models.DecimalField(decimal_places=2, default=50.0, max_digits=10)),
                ('reservation_expiry_days', models.PositiveIntegerField(default=7)),
                ('max_reservations_per_user', models.PositiveIntegerField(default=3)),
                ('library_name', models.CharField(default='School Library', max_length=200)),
                ('library_address', models.TextField(blank=True, null=True)),
                ('library_phone', models.CharField(blank=True, max_length=15, null=True)),
                ('library_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Library Settings',
                'verbose_name_plural': 'Library Settings',
            },
        ),
        migrations.CreateModel(
            name='Publisher',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, unique=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('website', models.URLField(blank=True, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('role', models.CharField(choices=[('admin', 'Administrator'), ('librarian', 'Librarian'), ('teacher', 'Teacher'), ('student', 'Student')], default='student', max_length=20)),
                ('phone_number', models.CharField(blank=True, max_length=15, null=True, validators=[django.core.validators.RegexValidator('^\\+?1?\\d{9,15}$', 'Enter a valid phone number.')])),
                ('address', models.TextField(blank=True, null=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('enrollment_number', models.CharField(blank=True, max_length=20, null=True, unique=True)),
                ('class_grade', models.CharField(blank=True, max_length=10, null=True)),
                ('profile_picture', models.ImageField(blank=True, null=True, upload_to='profile_pics/')),
                ('is_active_member', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Book',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=300)),
                ('subtitle', models.CharField(blank=True, max_length=300, null=True)),
                ('isbn', models.CharField(help_text='ISBN-10 or ISBN-13', max_length=17, unique=True, validators=[django.core.validators.RegexValidator('^[\\d-]{10,17}$', 'Enter a valid ISBN.')])),
                ('publication_date', models.DateField()),
                ('edition', models.CharField(blank=True, max_length=50, null=True)),
                ('pages', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('language', models.CharField(default='English', max_length=50)),
                ('cover_image', models.ImageField(blank=True, null=True, upload_to='book_covers/')),
                ('physical_description', models.TextField(blank=True, null=True)),
                ('shelf_location', models.CharField(help_text='e.g., A1, B2, C3', max_length=50)),
                ('section', models.CharField(help_text='e.g., Fiction, Science, History', max_length=100)),
                ('floor', models.CharField(default='Ground Floor', max_length=20)),
                ('status', models.CharField(choices=[('available', 'Available'), ('borrowed', 'Borrowed'), ('reserved', 'Reserved'), ('missing', 'Missing'), ('damaged', 'Damaged'), ('maintenance', 'Under Maintenance')], default='available', max_length=20)),
                ('total_copies', models.PositiveIntegerField(default=1, validators=[django.core.validators.MinValueValidator(1)])),
                ('available_copies', models.PositiveIntegerField(default=1, validators=[django.core.validators.MinValueValidator(0)])),
                ('summary', models.TextField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, help_text='Internal notes for librarians', null=True)),
                ('acquisition_date', models.DateField(default=django.utils.timezone.now)),
                ('price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('authors', models.ManyToManyField(related_name='books', to='library.author')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='books_created', to=settings.AUTH_USER_MODEL)),
                ('categories', models.ManyToManyField(related_name='books', to='library.category')),
                ('publisher', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='library.publisher')),
            ],
            options={
                'ordering': ['title'],
            },
        ),
        migrations.CreateModel(
            name='Reservation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('reservation_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('expiry_date', models.DateTimeField()),
                ('fulfilled_date', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('fulfilled', 'Fulfilled'), ('cancelled', 'Cancelled'), ('expired', 'Expired')], default='active', max_length=20)),
                ('priority', models.PositiveIntegerField(default=1)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reservations', to='library.book')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reservations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['priority', 'reservation_date'],
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('action', models.CharField(choices=[('book_added', 'Book Added'), ('book_updated', 'Book Updated'), ('book_deleted', 'Book Deleted'), ('loan_issued', 'Loan Issued'), ('loan_returned', 'Loan Returned'), ('loan_renewed', 'Loan Renewed'), ('reservation_made', 'Reservation Made'), ('reservation_cancelled', 'Reservation Cancelled'), ('user_created', 'User Created'), ('user_updated', 'User Updated'), ('settings_updated', 'Settings Updated')], max_length=50)),
                ('description', models.TextField()),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('target_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='audit_logs_target', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='audit_logs', to=settings.AUTH_USER_MODEL)),
                ('book', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='library.book')),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['action'], name='library_aud_action_18190c_idx'), models.Index(fields=['user'], name='library_aud_user_id_f67adc_idx'), models.Index(fields=['timestamp'], name='library_aud_timesta_649c41_idx')],
            },
        ),
        migrations.CreateModel(
            name='Loan',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('issue_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('due_date', models.DateTimeField()),
                ('return_date', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('returned', 'Returned'), ('overdue', 'Overdue'), ('lost', 'Lost'), ('damaged', 'Damaged')], default='active', max_length=20)),
                ('renewal_count', models.PositiveIntegerField(default=0, validators=[django.core.validators.MaxValueValidator(3)])),
                ('notes', models.TextField(blank=True, null=True)),
                ('fine_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('fine_paid', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='loans', to='library.book')),
                ('borrower', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='loans', to=settings.AUTH_USER_MODEL)),
                ('issued_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='loans_issued', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-issue_date'],
                'indexes': [models.Index(fields=['status'], name='library_loa_status_a19041_idx'), models.Index(fields=['due_date'], name='library_loa_due_dat_68a93d_idx'), models.Index(fields=['borrower'], name='library_loa_borrowe_94d367_idx'), models.Index(fields=['book'], name='library_loa_book_id_525a78_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='book',
            index=models.Index(fields=['title'], name='library_boo_title_c38ef2_idx'),
        ),
        migrations.AddIndex(
            model_name='book',
            index=models.Index(fields=['isbn'], name='library_boo_isbn_951e8b_idx'),
        ),
        migrations.AddIndex(
            model_name='book',
            index=models.Index(fields=['status'], name='library_boo_status_52a709_idx'),
        ),
        migrations.AddIndex(
            model_name='book',
            index=models.Index(fields=['shelf_location'], name='library_boo_shelf_l_80d358_idx'),
        ),
        migrations.AddIndex(
            model_name='reservation',
            index=models.Index(fields=['status'], name='library_res_status_e64963_idx'),
        ),
        migrations.AddIndex(
            model_name='reservation',
            index=models.Index(fields=['book', 'status'], name='library_res_book_id_8972ea_idx'),
        ),
        migrations.AddIndex(
            model_name='reservation',
            index=models.Index(fields=['user'], name='library_res_user_id_9e93c4_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='reservation',
            unique_together={('book', 'user', 'status')},
        ),
    ]
