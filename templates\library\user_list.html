{% extends 'base.html' %}

{% block title %}User Management - LibraryPro{% endblock %}

{% block content %}
<div class="space-y-8 animate-fade-in">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">User Management</h1>
            <p class="text-gray-600">Manage library members and their access</p>
        </div>
        <div class="mt-4 lg:mt-0">
            <a href="{% url 'register' %}" class="btn-primary px-6 py-3 rounded-xl text-white font-semibold flex items-center space-x-2 shadow-lg">
                <i class="fas fa-user-plus"></i>
                <span>Add New User</span>
            </a>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white rounded-2xl shadow-xl p-6">
        <form method="get" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                    <div class="relative">
                        <input type="text" name="search" value="{{ search_query }}" 
                               placeholder="Search by name, username, email..."
                               class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                    <select name="role" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300">
                        <option value="">All Roles</option>
                        {% for value, label in role_choices %}
                        <option value="{{ value }}" {% if role_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="flex items-end space-x-2">
                    <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300">
                        <i class="fas fa-search mr-2"></i>Filter
                    </button>
                    <a href="{% url 'user_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Users Table -->
    {% if users %}
    <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Academic Info</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for user_obj in users %}
                    <tr class="hover:bg-gray-50 transition-colors duration-200">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    {% if user_obj.profile_picture %}
                                    <img class="h-10 w-10 rounded-full object-cover" src="{{ user_obj.profile_picture.url }}" alt="">
                                    {% else %}
                                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                        <i class="fas fa-user text-gray-600"></i>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ user_obj.get_full_name|default:user_obj.username }}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        @{{ user_obj.username }}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ user_obj.email|default:"No email" }}</div>
                            {% if user_obj.phone_number %}
                            <div class="text-sm text-gray-500">{{ user_obj.phone_number }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if user_obj.role == 'admin' %}bg-purple-100 text-purple-800
                                {% elif user_obj.role == 'librarian' %}bg-blue-100 text-blue-800
                                {% elif user_obj.role == 'teacher' %}bg-green-100 text-green-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ user_obj.get_role_display }}
                            </span>
                            {% if user_obj.is_superuser %}
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 ml-1">
                                <i class="fas fa-crown text-xs mr-1"></i>Super
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {% if user_obj.enrollment_number %}
                            <div class="text-sm text-gray-900">ID: {{ user_obj.enrollment_number }}</div>
                            {% endif %}
                            {% if user_obj.class_grade %}
                            <div class="text-sm text-gray-500">{{ user_obj.class_grade }}</div>
                            {% endif %}
                            {% if not user_obj.enrollment_number and not user_obj.class_grade %}
                            <span class="text-gray-400">-</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if user_obj.is_active_member %}bg-green-100 text-green-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                <i class="fas fa-circle text-xs mr-1"></i>
                                {% if user_obj.is_active_member %}Active{% else %}Inactive{% endif %}
                            </span>
                            <div class="text-xs text-gray-500 mt-1">
                                Joined {{ user_obj.date_joined|date:"M Y" }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <a href="{% url 'user_detail' user_obj.id %}"
                                   class="text-blue-600 hover:text-blue-900 transition-colors duration-200"
                                   title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'user_edit' user_obj.id %}"
                                   class="text-gray-600 hover:text-gray-900 transition-colors duration-200"
                                   title="Edit User">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button onclick="toggleUserStatus('{{ user_obj.id }}', '{{ user_obj.is_active_member|yesno:"true,false" }}')"
                                        class="{% if user_obj.is_active_member %}text-red-600 hover:text-red-900{% else %}text-green-600 hover:text-green-900{% endif %} transition-colors duration-200"
                                        title="{% if user_obj.is_active_member %}Deactivate{% else %}Activate{% endif %} User">
                                    <i class="fas fa-{% if user_obj.is_active_member %}ban{% else %}check{% endif %}"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="flex justify-center">
        <nav class="flex space-x-2">
            {% if page_obj.has_previous %}
            <a href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}" 
               class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                First
            </a>
            <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}" 
               class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                Previous
            </a>
            {% endif %}

            <span class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium">
                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
            </span>

            {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}" 
               class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                Next
            </a>
            <a href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}" 
               class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                Last
            </a>
            {% endif %}
        </nav>
    </div>
    {% endif %}

    {% else %}
    <!-- No Users Found -->
    <div class="text-center py-16">
        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-users text-4xl text-gray-400"></i>
        </div>
        <h3 class="text-xl font-medium text-gray-500 mb-2">No users found</h3>
        <p class="text-gray-400 mb-6">No users match your current filters.</p>
        <a href="{% url 'register' %}" class="btn-primary px-6 py-3 rounded-xl text-white font-semibold">
            <i class="fas fa-user-plus mr-2"></i>Add First User
        </a>
    </div>
    {% endif %}
</div>

<script>
function toggleUserStatus(userId, isActive) {
    const action = isActive === 'true' ? 'deactivate' : 'activate';
    if (confirm(`Are you sure you want to ${action} this user?`)) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/users/${userId}/toggle-status/`;
        
        // Add CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken.value;
            form.appendChild(csrfInput);
        }
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
