{% extends 'base.html' %}

{% block title %}{{ title }} - Library Management{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">{{ title }}</h1>
            <p class="text-gray-600 mt-1">{{ description }}</p>
        </div>
        <a href="{{ add_url }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-plus mr-2"></i>Add {{ singular_name }}
        </a>
    </div>

    <!-- Search and Filter -->
    <div class="bg-white rounded-2xl shadow-xl p-6">
        <form method="get" class="flex space-x-4">
            <div class="flex-1">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                        <i class="fas fa-search text-gray-400 text-sm"></i>
                    </div>
                    <input type="text" name="search" value="{{ request.GET.search|default:'' }}"
                           placeholder="Search {{ title|lower }}..."
                           class="pl-12 w-full pr-4 py-3 border-2 border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm">
                </div>
            </div>
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg">
                <i class="fas fa-search mr-2"></i>Search
            </button>
            <a href="{{ list_url }}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg">
                <i class="fas fa-times mr-2"></i>Clear
            </a>
        </form>
    </div>

    <!-- Items List -->
    {% if items %}
    <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        {% for header in table_headers %}
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ header }}
                        </th>
                        {% endfor %}
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for item in items %}
                    <tr class="hover:bg-gray-50 transition-colors duration-200">
                        {% for field in item.display_fields %}
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ field }}</div>
                        </td>
                        {% endfor %}
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <a href="{{ item.edit_url }}" class="text-blue-600 hover:text-blue-900 transition-colors duration-200" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ item.delete_url }}" class="text-red-600 hover:text-red-900 transition-colors duration-200" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="flex justify-center">
        <nav class="flex space-x-2">
            {% if page_obj.has_previous %}
            <a href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" 
               class="px-3 py-2 bg-white border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">
                First
            </a>
            <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" 
               class="px-3 py-2 bg-white border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">
                Previous
            </a>
            {% endif %}

            <span class="px-3 py-2 bg-blue-600 text-white rounded-md text-sm">
                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
            </span>

            {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" 
               class="px-3 py-2 bg-white border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">
                Next
            </a>
            <a href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" 
               class="px-3 py-2 bg-white border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">
                Last
            </a>
            {% endif %}
        </nav>
    </div>
    {% endif %}

    {% else %}
    <!-- No Items Found -->
    <div class="text-center py-12">
        <i class="fas {{ empty_icon }} text-6xl text-gray-300 mb-4"></i>
        <h3 class="text-xl font-medium text-gray-500 mb-2">No {{ title|lower }} found</h3>
        <p class="text-gray-400">{{ empty_message }}</p>
        <a href="{{ add_url }}" class="mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
            <i class="fas fa-plus mr-2"></i>Add First {{ singular_name }}
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}
