# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON>, 2014
# <PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2014-2015,2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-01-15 16:04+0000\n"
"Last-Translator: palmux <<EMAIL>>\n"
"Language-Team: Italian (http://www.transifex.com/django/django/language/"
"it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: it\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Redirezioni"

msgid "site"
msgstr "sito"

msgid "redirect from"
msgstr "redirezione da"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Deve essere un percorso assoluto, senza nome a dominio. Esempio: '/events/"
"search/'."

msgid "redirect to"
msgstr "redirezione verso"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Può essere un percorso assoluto (come sopra) o un URL completo che inizia "
"con \"https: //\"."

msgid "redirect"
msgstr "redirezione"

msgid "redirects"
msgstr "redirezioni"
