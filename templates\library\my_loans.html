{% extends 'base.html' %}
{% load static %}

{% block title %}My Loans - Library Management System{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">My Loans</h1>
                <p class="text-gray-600 mt-2">Track your current and past book loans</p>
            </div>
            <div class="flex space-x-4">
                <a href="{% url 'book_list' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-sm">
                    <i class="fas fa-search mr-2"></i>Browse Books
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-book-reader text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Active Loans</p>
                    <p class="text-2xl font-bold text-gray-900">{{ active_loans.count }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Overdue</p>
                    <p class="text-2xl font-bold text-gray-900">{{ overdue_loans.count }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Returned</p>
                    <p class="text-2xl font-bold text-gray-900">{{ returned_loans.count }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-history text-purple-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Loans</p>
                    <p class="text-2xl font-bold text-gray-900">{{ total_loans.count }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-8">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                <button onclick="showTab('active')" id="active-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Active Loans ({{ active_loans.count }})
                </button>
                <button onclick="showTab('history')" id="history-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Loan History ({{ total_loans.count }})
                </button>
            </nav>
        </div>

        <!-- Active Loans Tab -->
        <div id="active-content" class="tab-content">
            <div class="p-6">
                {% if active_loans %}
                <div class="space-y-4">
                    {% for loan in active_loans %}
                    <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow duration-200">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start space-x-4">
                                {% if loan.book.cover_image %}
                                <img src="{{ loan.book.cover_image.url }}" alt="{{ loan.book.title }}" class="w-16 h-20 object-cover rounded-lg">
                                {% else %}
                                <div class="w-16 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-book text-gray-400"></i>
                                </div>
                                {% endif %}
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900">{{ loan.book.title }}</h3>
                                    {% if loan.book.subtitle %}
                                    <p class="text-gray-600">{{ loan.book.subtitle }}</p>
                                    {% endif %}
                                    <p class="text-sm text-gray-500 mt-1">{{ loan.book.get_authors_display }}</p>
                                    <div class="mt-3 grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span class="font-medium text-gray-700">Issued:</span>
                                            <span class="text-gray-600">{{ loan.issue_date|date:"M d, Y" }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-700">Due:</span>
                                            <span class="{% if loan.is_overdue %}text-red-600 font-medium{% else %}text-gray-600{% endif %}">
                                                {{ loan.due_date|date:"M d, Y" }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-col items-end space-y-2">
                                {% if loan.is_overdue %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>Overdue
                                </span>
                                {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>Active
                                </span>
                                {% endif %}
                                {% if loan.days_until_due <= 3 and not loan.is_overdue %}
                                <span class="text-xs text-orange-600 font-medium">
                                    Due in {{ loan.days_until_due }} day{{ loan.days_until_due|pluralize }}
                                </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-book-reader text-gray-300 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Active Loans</h3>
                    <p class="text-gray-500 mb-6">You don't have any books currently borrowed.</p>
                    <a href="{% url 'book_list' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200">
                        Browse Books
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Loan History Tab -->
        <div id="history-content" class="tab-content hidden">
            <div class="p-6">
                {% if total_loans %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Book</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Return Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for loan in total_loans %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        {% if loan.book.cover_image %}
                                        <img src="{{ loan.book.cover_image.url }}" alt="{{ loan.book.title }}" class="w-10 h-12 object-cover rounded mr-4">
                                        {% else %}
                                        <div class="w-10 h-12 bg-gray-200 rounded flex items-center justify-center mr-4">
                                            <i class="fas fa-book text-gray-400 text-xs"></i>
                                        </div>
                                        {% endif %}
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ loan.book.title }}</div>
                                            <div class="text-sm text-gray-500">{{ loan.book.get_authors_display }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ loan.issue_date|date:"M d, Y" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ loan.due_date|date:"M d, Y" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {% if loan.return_date %}
                                        {{ loan.return_date|date:"M d, Y" }}
                                    {% else %}
                                        <span class="text-gray-400">Not returned</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if loan.status == 'active' %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                                    {% elif loan.status == 'returned' %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Returned</span>
                                    {% elif loan.status == 'overdue' %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Overdue</span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">{{ loan.get_status_display }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-history text-gray-300 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Loan History</h3>
                    <p class="text-gray-500">You haven't borrowed any books yet.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Remove active class from all tabs
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show selected tab content
    document.getElementById(tabName + '-content').classList.remove('hidden');
    
    // Add active class to selected tab
    const activeTab = document.getElementById(tabName + '-tab');
    activeTab.classList.remove('border-transparent', 'text-gray-500');
    activeTab.classList.add('border-blue-500', 'text-blue-600');
}

// Initialize with active tab
document.addEventListener('DOMContentLoaded', function() {
    showTab('active');
});
</script>
{% endblock %}
