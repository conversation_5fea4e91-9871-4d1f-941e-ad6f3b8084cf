{% extends 'base.html' %}

{% block title %}{{ title }} - LibraryPro{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto animate-fade-in">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-4xl font-bold text-gray-800 mb-2">{{ title }}</h1>
        <p class="text-gray-600">Create a new book loan for a library member</p>
    </div>

    <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
        <!-- Form Header -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 px-8 py-6 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                <i class="fas fa-hand-holding text-blue-600 mr-3"></i>
                Loan Information
            </h2>
        </div>

        <!-- Form Content -->
        <form method="post" class="p-8 space-y-8">
            {% csrf_token %}
            
            <!-- Book Selection -->
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-book text-green-600 mr-2"></i>
                    Select Book
                </h3>
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label for="{{ form.book.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Available Books *
                        </label>
                        <select name="{{ form.book.name }}" id="{{ form.book.id_for_label }}" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                                required onchange="updateBookInfo(this)">
                            <option value="">Select a book to loan...</option>
                            {% for book in form.book.queryset %}
                            <option value="{{ book.id }}" data-isbn="{{ book.isbn }}" data-location="{{ book.section }} - {{ book.shelf_location }}" data-copies="{{ book.available_copies }}">
                                {{ book.title }} - {{ book.get_authors_display }} ({{ book.available_copies }} available)
                            </option>
                            {% endfor %}
                        </select>
                        {% if form.book.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.book.errors.0 }}
                        </div>
                        {% endif %}
                        
                        <!-- Book Info Display -->
                        <div id="bookInfo" class="mt-4 p-4 bg-gray-50 rounded-xl hidden">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span class="font-medium text-gray-700">ISBN:</span>
                                    <span id="bookISBN" class="text-gray-600 ml-1">-</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Location:</span>
                                    <span id="bookLocation" class="text-gray-600 ml-1">-</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Available:</span>
                                    <span id="bookCopies" class="text-gray-600 ml-1">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Borrower Selection -->
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-user text-purple-600 mr-2"></i>
                    Select Borrower
                </h3>
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label for="{{ form.borrower.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Library Members *
                        </label>
                        <select name="{{ form.borrower.name }}" id="{{ form.borrower.id_for_label }}" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                                required onchange="updateBorrowerInfo(this)">
                            <option value="">Select a borrower...</option>
                            {% for user in form.borrower.queryset %}
                            <option value="{{ user.id }}" data-role="{{ user.get_role_display }}" data-email="{{ user.email }}" data-enrollment="{{ user.enrollment_number|default:'-' }}">
                                {{ user.get_full_name }} ({{ user.get_role_display }})
                            </option>
                            {% endfor %}
                        </select>
                        {% if form.borrower.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.borrower.errors.0 }}
                        </div>
                        {% endif %}
                        
                        <!-- Borrower Info Display -->
                        <div id="borrowerInfo" class="mt-4 p-4 bg-gray-50 rounded-xl hidden">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span class="font-medium text-gray-700">Role:</span>
                                    <span id="borrowerRole" class="text-gray-600 ml-1">-</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Email:</span>
                                    <span id="borrowerEmail" class="text-gray-600 ml-1">-</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">ID:</span>
                                    <span id="borrowerEnrollment" class="text-gray-600 ml-1">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loan Details -->
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-calendar text-orange-600 mr-2"></i>
                    Loan Details
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.due_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Due Date *
                        </label>
                        <input type="datetime-local" 
                               name="{{ form.due_date.name }}" 
                               id="{{ form.due_date.id_for_label }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                               required>
                        {% if form.due_date.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.due_date.errors.0 }}
                        </div>
                        {% endif %}
                        <div class="mt-2 text-sm text-gray-500">
                            <i class="fas fa-info-circle mr-1"></i>
                            Default loan period is 14 days
                        </div>
                    </div>
                    
                    <div>
                        <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Notes (Optional)
                        </label>
                        <textarea name="{{ form.notes.name }}" 
                                  id="{{ form.notes.id_for_label }}"
                                  rows="4"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                                  placeholder="Add any special notes about this loan..."></textarea>
                        {% if form.notes.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.notes.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{% url 'loan_list' %}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-8 py-3 rounded-xl font-medium transition-all duration-300">
                    <i class="fas fa-times mr-2"></i>Cancel
                </a>
                <button type="submit" 
                        class="btn-primary px-8 py-3 rounded-xl text-white font-medium shadow-lg">
                    <i class="fas fa-hand-holding mr-2"></i>Issue Loan
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Set default due date (14 days from now)
document.addEventListener('DOMContentLoaded', function() {
    const dueDateInput = document.getElementById('{{ form.due_date.id_for_label }}');
    const now = new Date();
    now.setDate(now.getDate() + 14);
    
    // Format date for datetime-local input
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    
    dueDateInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
});

function updateBookInfo(select) {
    const bookInfo = document.getElementById('bookInfo');
    const selectedOption = select.options[select.selectedIndex];
    
    if (selectedOption.value) {
        document.getElementById('bookISBN').textContent = selectedOption.dataset.isbn || '-';
        document.getElementById('bookLocation').textContent = selectedOption.dataset.location || '-';
        document.getElementById('bookCopies').textContent = selectedOption.dataset.copies || '-';
        bookInfo.classList.remove('hidden');
    } else {
        bookInfo.classList.add('hidden');
    }
}

function updateBorrowerInfo(select) {
    const borrowerInfo = document.getElementById('borrowerInfo');
    const selectedOption = select.options[select.selectedIndex];
    
    if (selectedOption.value) {
        document.getElementById('borrowerRole').textContent = selectedOption.dataset.role || '-';
        document.getElementById('borrowerEmail').textContent = selectedOption.dataset.email || '-';
        document.getElementById('borrowerEnrollment').textContent = selectedOption.dataset.enrollment || '-';
        borrowerInfo.classList.remove('hidden');
    } else {
        borrowerInfo.classList.add('hidden');
    }
}
</script>
{% endblock %}
