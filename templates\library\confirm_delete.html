{% extends 'base.html' %}

{% block title %}Delete {{ object_name }} - Library Management{% endblock %}

{% block content %}
<div class="max-w-md mx-auto space-y-6">
    <!-- Header -->
    <div class="flex items-center space-x-4">
        <a href="{{ back_url }}" class="text-gray-600 hover:text-gray-900">
            <i class="fas fa-arrow-left text-xl"></i>
        </a>
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Confirm Delete</h1>
            <p class="text-gray-600 mt-1">This action cannot be undone</p>
        </div>
    </div>

    <!-- Confirmation Card -->
    <div class="bg-white rounded-2xl shadow-xl p-8">
        <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Delete {{ object_name }}?</h3>
            <p class="text-sm text-gray-500 mb-6">
                Are you sure you want to delete "{{ object_name }}"? This action cannot be undone.
            </p>
            
            <form method="post" class="flex justify-center space-x-4">
                {% csrf_token %}
                <a href="{{ back_url }}" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-200">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all duration-200 shadow-sm">
                    <i class="fas fa-trash mr-2"></i>Delete
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}
