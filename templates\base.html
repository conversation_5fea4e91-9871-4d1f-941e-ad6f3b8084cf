<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Library Management System{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-blue-600 text-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{% url 'home' %}" class="flex items-center space-x-2">
                        <i class="fas fa-book text-2xl"></i>
                        <span class="text-xl font-bold">Library Management</span>
                    </a>
                </div>
                
                {% if user.is_authenticated %}
                <div class="flex items-center space-x-4">
                    <!-- Navigation Links -->
                    <div class="hidden md:flex space-x-4">
                        <a href="{% url 'home' %}" class="hover:bg-blue-700 px-3 py-2 rounded">
                            <i class="fas fa-home mr-1"></i>Dashboard
                        </a>
                        <a href="{% url 'book_list' %}" class="hover:bg-blue-700 px-3 py-2 rounded">
                            <i class="fas fa-book mr-1"></i>Books
                        </a>
                        {% if user.can_manage_books %}
                        <a href="{% url 'book_add' %}" class="hover:bg-blue-700 px-3 py-2 rounded">
                            <i class="fas fa-plus mr-1"></i>Add Book
                        </a>
                        {% endif %}
                    </div>
                    
                    <!-- User Menu -->
                    <div class="relative group">
                        <button class="flex items-center space-x-2 hover:bg-blue-700 px-3 py-2 rounded">
                            <i class="fas fa-user"></i>
                            <span>{{ user.get_full_name|default:user.username }}</span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                        
                        <!-- Dropdown Menu -->
                        <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
                            <a href="{% url 'profile' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user mr-2"></i>Profile
                            </a>
                            {% if user.can_manage_users %}
                            <a href="{% url 'register' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-plus mr-2"></i>Add User
                            </a>
                            {% endif %}
                            <div class="border-t border-gray-100"></div>
                            <a href="{% url 'logout' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt mr-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} mb-4 p-4 rounded-md {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'warning' %}bg-yellow-100 border border-yellow-400 text-yellow-700{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}">
            <div class="flex">
                <div class="flex-shrink-0">
                    {% if message.tags == 'error' %}
                    <i class="fas fa-exclamation-circle"></i>
                    {% elif message.tags == 'warning' %}
                    <i class="fas fa-exclamation-triangle"></i>
                    {% elif message.tags == 'success' %}
                    <i class="fas fa-check-circle"></i>
                    {% else %}
                    <i class="fas fa-info-circle"></i>
                    {% endif %}
                </div>
                <div class="ml-3">
                    {{ message }}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-auto">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="text-center">
                <p>&copy; 2024 Library Management System. All rights reserved.</p>
            </div>
        </div>
    </footer>

    {% block extra_js %}{% endblock %}
</body>
</html>
