/* Library Management System - Custom Styles */

/* Form Field Icon Fixes */
.form-field-with-icon {
    position: relative;
}

.form-field-with-icon .field-icon {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    padding-left: 1rem;
    pointer-events: none;
    z-index: 10;
    color: #9CA3AF; /* text-gray-400 */
    font-size: 0.875rem; /* text-sm */
}

.form-field-with-icon .field-input {
    padding-left: 3rem !important; /* pl-12 equivalent */
}

.form-field-with-icon .field-input-currency {
    padding-left: 2.5rem !important; /* pl-10 equivalent for $ symbol */
}

/* Search Field Specific Styles */
.search-field {
    position: relative;
}

.search-field .search-icon {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    padding-left: 1rem;
    pointer-events: none;
    z-index: 10;
    color: #9CA3AF;
    font-size: 0.875rem;
}

.search-field .search-input {
    padding-left: 3rem !important;
    width: 100%;
    padding-right: 1rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    border: 2px solid #D1D5DB;
    border-radius: 0.5rem;
    background-color: white;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.search-field .search-input:focus {
    outline: none;
    ring: 2px;
    ring-color: #3B82F6;
    border-color: #3B82F6;
}

/* Form Input Base Styles */
.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #D1D5DB;
    border-radius: 0.5rem;
    background-color: white;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.form-input:focus {
    outline: none;
    ring: 2px;
    ring-color: #3B82F6;
    border-color: #3B82F6;
}

/* Button Styles */
.btn-primary {
    background-color: #2563EB;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    background-color: #1D4ED8;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background-color: #6B7280;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    border: none;
    cursor: pointer;
}

.btn-secondary:hover {
    background-color: #4B5563;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Sidebar Enhancements */
.sidebar-item {
    transition: all 0.2s ease;
}

.sidebar-item:hover {
    background: rgba(59, 130, 246, 0.1);
    border-left: 4px solid #3B82F6;
}

/* Card Styles */
.card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #E5E7EB;
}

.card-body {
    padding: 1.5rem;
}

/* Table Enhancements */
.table-hover tbody tr:hover {
    background-color: #F9FAFB;
    transition: background-color 0.2s ease;
}

/* Notification Styles */
.notification-badge {
    position: absolute;
    top: -0.25rem;
    right: -0.25rem;
    background-color: #EF4444;
    color: white;
    font-size: 0.75rem;
    border-radius: 9999px;
    height: 1rem;
    width: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Loading Spinner */
.spinner {
    border: 2px solid #F3F4F6;
    border-top: 2px solid #3B82F6;
    border-radius: 50%;
    width: 1rem;
    height: 1rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .form-field-with-icon .field-input {
        padding-left: 2.5rem !important;
    }
    
    .search-field .search-input {
        padding-left: 2.5rem !important;
    }
    
    .form-field-with-icon .field-icon,
    .search-field .search-icon {
        padding-left: 0.75rem;
    }
}

/* Dark mode support (if needed later) */
@media (prefers-color-scheme: dark) {
    .form-input {
        background-color: #374151;
        border-color: #4B5563;
        color: white;
    }
    
    .card {
        background-color: #1F2937;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Form Validation Styles */
.form-error {
    border-color: #EF4444 !important;
    ring-color: #EF4444 !important;
}

.form-success {
    border-color: #10B981 !important;
    ring-color: #10B981 !important;
}

.error-message {
    color: #EF4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.success-message {
    color: #10B981;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
