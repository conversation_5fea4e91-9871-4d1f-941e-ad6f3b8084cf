{% extends 'base.html' %}

{% block title %}Books - Library Management System{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-3xl font-bold text-gray-800">Books</h1>
        {% if user.can_manage_books %}
        <a href="{% url 'book_add' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-plus mr-2"></i>Add Book
        </a>
        {% endif %}
    </div>

    <!-- Search and Filters -->
    <div class="bg-white rounded-lg shadow p-6">
        <form method="get" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="{{ form.query.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    {{ form.query }}
                </div>
                <div>
                    <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    {{ form.category }}
                </div>
                <div>
                    <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    {{ form.status }}
                </div>
                <div>
                    <label for="{{ form.section.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Section</label>
                    {{ form.section }}
                </div>
            </div>
            <div class="flex space-x-2">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-search mr-2"></i>Search
                </button>
                <a href="{% url 'book_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-times mr-2"></i>Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Books Grid -->
    {% if books %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {% for book in books %}
        <div class="bg-white rounded-lg shadow hover:shadow-lg transition duration-200">
            <div class="p-4">
                <!-- Book Cover -->
                <div class="aspect-w-3 aspect-h-4 mb-4">
                    {% if book.cover_image %}
                    <img src="{{ book.cover_image.url }}" alt="{{ book.title }}" class="w-full h-48 object-cover rounded">
                    {% else %}
                    <div class="w-full h-48 bg-gray-200 rounded flex items-center justify-center">
                        <i class="fas fa-book text-4xl text-gray-400"></i>
                    </div>
                    {% endif %}
                </div>

                <!-- Book Info -->
                <div class="space-y-2">
                    <h3 class="font-bold text-lg text-gray-800 line-clamp-2">{{ book.title }}</h3>
                    <p class="text-sm text-gray-600">by {{ book.get_authors_display }}</p>
                    <p class="text-sm text-gray-500">ISBN: {{ book.isbn }}</p>
                    
                    <!-- Status Badge -->
                    <div class="flex items-center justify-between">
                        <span class="inline-block px-2 py-1 text-xs font-semibold rounded
                            {% if book.status == 'available' %}bg-green-100 text-green-800
                            {% elif book.status == 'borrowed' %}bg-yellow-100 text-yellow-800
                            {% elif book.status == 'reserved' %}bg-blue-100 text-blue-800
                            {% else %}bg-red-100 text-red-800{% endif %}">
                            {{ book.get_status_display }}
                        </span>
                        <span class="text-sm text-gray-500">{{ book.available_copies }}/{{ book.total_copies }} available</span>
                    </div>

                    <!-- Location -->
                    <p class="text-sm text-gray-500">
                        <i class="fas fa-map-marker-alt mr-1"></i>{{ book.section }} - {{ book.shelf_location }}
                    </p>
                </div>

                <!-- Actions -->
                <div class="mt-4 flex space-x-2">
                    <a href="{% url 'book_detail' book.id %}" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 rounded text-sm">
                        View Details
                    </a>
                    {% if user.can_manage_books %}
                    <a href="{% url 'book_edit' book.id %}" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm">
                        <i class="fas fa-edit"></i>
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="flex justify-center">
        <nav class="flex space-x-2">
            {% if page_obj.has_previous %}
            <a href="?page=1{% if request.GET.query %}&query={{ request.GET.query }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.section %}&section={{ request.GET.section }}{% endif %}" 
               class="px-3 py-2 bg-white border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">
                First
            </a>
            <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.query %}&query={{ request.GET.query }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.section %}&section={{ request.GET.section }}{% endif %}" 
               class="px-3 py-2 bg-white border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">
                Previous
            </a>
            {% endif %}

            <span class="px-3 py-2 bg-blue-600 text-white rounded-md text-sm">
                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
            </span>

            {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if request.GET.query %}&query={{ request.GET.query }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.section %}&section={{ request.GET.section }}{% endif %}" 
               class="px-3 py-2 bg-white border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">
                Next
            </a>
            <a href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.query %}&query={{ request.GET.query }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.section %}&section={{ request.GET.section }}{% endif %}" 
               class="px-3 py-2 bg-white border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">
                Last
            </a>
            {% endif %}
        </nav>
    </div>
    {% endif %}

    {% else %}
    <!-- No Books Found -->
    <div class="text-center py-12">
        <i class="fas fa-book text-6xl text-gray-300 mb-4"></i>
        <h3 class="text-xl font-medium text-gray-500 mb-2">No books found</h3>
        <p class="text-gray-400">Try adjusting your search criteria or add some books to the library.</p>
        {% if user.can_manage_books %}
        <a href="{% url 'book_add' %}" class="mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
            <i class="fas fa-plus mr-2"></i>Add First Book
        </a>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %}
