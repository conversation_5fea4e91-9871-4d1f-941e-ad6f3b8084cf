# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# Jon, 2015
# Jon, 2014
# Jon, 2020-2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-03-18 14:19+0000\n"
"Last-Translator: Jon\n"
"Language-Team: Norwegian Bokmål (http://www.transifex.com/django/django/"
"language/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Omadresseringer"

msgid "site"
msgstr "nettsted"

msgid "redirect from"
msgstr "omadresser fra"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Dette bør være en fullstendig sti uten domenenavn. Eksempel: \"/hendelser/"
"finn/\"."

msgid "redirect to"
msgstr "omadresser til"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Dette kan være enten en full sti (som over) eller en full URL som starter "
"med en protokoll som “https://”."

msgid "redirect"
msgstr "omadressering"

msgid "redirects"
msgstr "omadresseringer"
