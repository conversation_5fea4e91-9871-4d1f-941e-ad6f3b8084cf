# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON>, 2014-2015,2019,2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-03-21 13:00+0000\n"
"Last-Translator: <PERSON><PERSON>\n"
"Language-Team: Spanish (Argentina) (http://www.transifex.com/django/django/"
"language/es_AR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es_AR\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Redirecciones"

msgid "site"
msgstr "sitio"

msgid "redirect from"
msgstr "redirigir desde"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Esta ruta debe ser absoluta, excluyendo el nombre de dominio. Ejemplo: “/"
"events/search”."

msgid "redirect to"
msgstr "redirigir a"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Esto puede ser bien una ruta absoluta (como arriba) o una URL completa que "
"empiece con un esquema, por ejemplo “https://”."

msgid "redirect"
msgstr "redirección"

msgid "redirects"
msgstr "redirecciones"
