{% extends 'base.html' %}

{% block title %}Dashboard - Library Management System{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-white rounded-lg shadow p-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">
            Welcome, {{ user.get_full_name|default:user.username }}!
        </h1>
        <p class="text-gray-600">
            Role: <span class="font-semibold text-blue-600">{{ user.get_role_display }}</span>
        </p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-book text-3xl text-blue-500"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Books</p>
                    <p class="text-2xl font-bold text-gray-900">{{ total_books }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-3xl text-green-500"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Available Books</p>
                    <p class="text-2xl font-bold text-gray-900">{{ available_books }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-users text-3xl text-purple-500"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Active Users</p>
                    <p class="text-2xl font-bold text-gray-900">{{ total_users }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-hand-holding text-3xl text-orange-500"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Active Loans</p>
                    <p class="text-2xl font-bold text-gray-900">{{ active_loans }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{% url 'book_list' %}" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition duration-200">
                <i class="fas fa-search text-blue-600 text-xl mr-3"></i>
                <span class="font-medium text-blue-800">Browse Books</span>
            </a>
            
            {% if user.can_manage_books %}
            <a href="{% url 'book_add' %}" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition duration-200">
                <i class="fas fa-plus text-green-600 text-xl mr-3"></i>
                <span class="font-medium text-green-800">Add New Book</span>
            </a>
            {% endif %}
            
            <a href="{% url 'profile' %}" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition duration-200">
                <i class="fas fa-user text-purple-600 text-xl mr-3"></i>
                <span class="font-medium text-purple-800">My Profile</span>
            </a>
        </div>
    </div>

    <!-- Personal Section for Students/Teachers -->
    {% if user.can_borrow_books %}
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- My Current Loans -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">My Current Loans</h2>
            {% if my_loans %}
            <div class="space-y-3">
                {% for loan in my_loans %}
                <div class="border-l-4 border-blue-500 pl-4 py-2">
                    <h3 class="font-semibold text-gray-800">{{ loan.book.title }}</h3>
                    <p class="text-sm text-gray-600">Due: {{ loan.due_date|date:"M d, Y" }}</p>
                    {% if loan.is_overdue %}
                    <span class="inline-block px-2 py-1 text-xs font-semibold bg-red-100 text-red-800 rounded">Overdue</span>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p class="text-gray-500">No current loans</p>
            {% endif %}
        </div>

        <!-- My Reservations -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">My Reservations</h2>
            {% if my_reservations %}
            <div class="space-y-3">
                {% for reservation in my_reservations %}
                <div class="border-l-4 border-yellow-500 pl-4 py-2">
                    <h3 class="font-semibold text-gray-800">{{ reservation.book.title }}</h3>
                    <p class="text-sm text-gray-600">Reserved: {{ reservation.reservation_date|date:"M d, Y" }}</p>
                    <p class="text-sm text-gray-600">Position: #{{ reservation.priority }}</p>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p class="text-gray-500">No active reservations</p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Admin/Librarian Section -->
    {% if user.can_manage_books %}
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Loans -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Recent Loans</h2>
            {% if recent_loans %}
            <div class="space-y-3">
                {% for loan in recent_loans %}
                <div class="border-l-4 border-green-500 pl-4 py-2">
                    <h3 class="font-semibold text-gray-800">{{ loan.book.title }}</h3>
                    <p class="text-sm text-gray-600">Borrower: {{ loan.borrower.get_full_name }}</p>
                    <p class="text-sm text-gray-600">Issued: {{ loan.issue_date|date:"M d, Y" }}</p>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p class="text-gray-500">No recent loans</p>
            {% endif %}
        </div>

        <!-- Active Reservations -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Active Reservations</h2>
            {% if recent_reservations %}
            <div class="space-y-3">
                {% for reservation in recent_reservations %}
                <div class="border-l-4 border-yellow-500 pl-4 py-2">
                    <h3 class="font-semibold text-gray-800">{{ reservation.book.title }}</h3>
                    <p class="text-sm text-gray-600">User: {{ reservation.user.get_full_name }}</p>
                    <p class="text-sm text-gray-600">Reserved: {{ reservation.reservation_date|date:"M d, Y" }}</p>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p class="text-gray-500">No active reservations</p>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
