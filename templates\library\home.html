{% extends 'base.html' %}

{% block title %}Dashboard - LibraryPro{% endblock %}

{% block content %}
<div class="space-y-8 animate-fade-in">
    <!-- Welcome Section -->
    <div class="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 rounded-3xl shadow-2xl">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
        <div class="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>

        <div class="relative px-8 py-12">
            <div class="flex items-center justify-between">
                <div>
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                            {% if user.profile_picture %}
                            <img src="{{ user.profile_picture.url }}" alt="Profile" class="w-16 h-16 rounded-2xl object-cover">
                            {% else %}
                            <i class="fas fa-user text-2xl text-white"></i>
                            {% endif %}
                        </div>
                        <div>
                            <h1 class="text-4xl font-bold text-white mb-2">
                                Welcome back, {{ user.get_full_name|default:user.username }}! 👋
                            </h1>
                            <div class="flex items-center space-x-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/20 text-white backdrop-blur-sm">
                                    <i class="fas fa-user-tag mr-2"></i>{{ user.get_role_display }}
                                </span>
                                <span class="text-white/80 text-sm">
                                    <i class="fas fa-clock mr-1"></i>
                                    Last login: Today
                                </span>
                            </div>
                        </div>
                    </div>
                    <p class="text-white/90 text-lg max-w-2xl">
                        Ready to manage your library efficiently? Explore books, track loans, and keep everything organized.
                    </p>
                </div>

                <div class="hidden lg:block">
                    <div class="w-32 h-32 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm animate-bounce-subtle">
                        <i class="fas fa-book-open text-4xl text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Books Card -->
        <div class="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 card-hover overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500 mb-1">Total Books</p>
                        <p class="text-3xl font-bold text-gray-900 mb-2">{{ total_books|default:0 }}</p>
                        <div class="flex items-center text-sm">
                            <span class="text-green-600 font-medium">
                                <i class="fas fa-arrow-up mr-1"></i>12%
                            </span>
                            <span class="text-gray-500 ml-1">vs last month</span>
                        </div>
                    </div>
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-book text-2xl text-white"></i>
                    </div>
                </div>
            </div>
            <div class="h-2 bg-gradient-to-r from-blue-500 to-blue-600"></div>
        </div>

        <!-- Available Books Card -->
        <div class="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 card-hover overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500 mb-1">Available Books</p>
                        <p class="text-3xl font-bold text-gray-900 mb-2">{{ available_books|default:0 }}</p>
                        <div class="flex items-center text-sm">
                            <span class="text-green-600 font-medium">
                                <i class="fas fa-check-circle mr-1"></i>Ready to borrow
                            </span>
                        </div>
                    </div>
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-check-circle text-2xl text-white"></i>
                    </div>
                </div>
            </div>
            <div class="h-2 bg-gradient-to-r from-green-500 to-green-600"></div>
        </div>

        <!-- Active Users Card -->
        <div class="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 card-hover overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500 mb-1">Active Users</p>
                        <p class="text-3xl font-bold text-gray-900 mb-2">{{ total_users|default:0 }}</p>
                        <div class="flex items-center text-sm">
                            <span class="text-purple-600 font-medium">
                                <i class="fas fa-user-check mr-1"></i>Registered members
                            </span>
                        </div>
                    </div>
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-users text-2xl text-white"></i>
                    </div>
                </div>
            </div>
            <div class="h-2 bg-gradient-to-r from-purple-500 to-purple-600"></div>
        </div>

        <!-- Active Loans Card -->
        <div class="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 card-hover overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500 mb-1">Active Loans</p>
                        <p class="text-3xl font-bold text-gray-900 mb-2">{{ active_loans|default:0 }}</p>
                        <div class="flex items-center text-sm">
                            {% if overdue_loans %}
                            <span class="text-red-600 font-medium">
                                <i class="fas fa-exclamation-triangle mr-1"></i>{{ overdue_loans }} overdue
                            </span>
                            {% else %}
                            <span class="text-orange-600 font-medium">
                                <i class="fas fa-hand-holding mr-1"></i>Currently borrowed
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-hand-holding text-2xl text-white"></i>
                    </div>
                </div>
            </div>
            <div class="h-2 bg-gradient-to-r from-orange-500 to-orange-600"></div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-2xl shadow-xl p-8">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-bolt text-yellow-500 mr-3"></i>
                Quick Actions
            </h2>
            <div class="w-12 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {% if user.can_borrow_books %}
            <!-- Student Dashboard -->
            <a href="{% url 'student_dashboard' %}" class="group relative overflow-hidden bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-2xl p-6 hover:from-indigo-100 hover:to-indigo-200 transition-all duration-300 card-hover">
                <div class="absolute top-0 right-0 w-20 h-20 bg-indigo-500/10 rounded-full -translate-y-10 translate-x-10"></div>
                <div class="relative">
                    <div class="w-12 h-12 bg-indigo-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-user-graduate text-white text-lg"></i>
                    </div>
                    <h3 class="font-semibold text-indigo-800 mb-2">My Library</h3>
                    <p class="text-indigo-600 text-sm">View loans & borrow books</p>
                </div>
            </a>
            {% endif %}

            <!-- Browse Books -->
            <a href="{% url 'book_list' %}" class="group relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 hover:from-blue-100 hover:to-blue-200 transition-all duration-300 card-hover">
                <div class="absolute top-0 right-0 w-20 h-20 bg-blue-500/10 rounded-full -translate-y-10 translate-x-10"></div>
                <div class="relative">
                    <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-search text-white text-lg"></i>
                    </div>
                    <h3 class="font-semibold text-blue-800 mb-2">Browse Books</h3>
                    <p class="text-blue-600 text-sm">Explore our collection</p>
                </div>
            </a>

            {% if user.can_manage_books %}
            <!-- Add New Book -->
            <a href="{% url 'book_add' %}" class="group relative overflow-hidden bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 hover:from-green-100 hover:to-green-200 transition-all duration-300 card-hover">
                <div class="absolute top-0 right-0 w-20 h-20 bg-green-500/10 rounded-full -translate-y-10 translate-x-10"></div>
                <div class="relative">
                    <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-plus text-white text-lg"></i>
                    </div>
                    <h3 class="font-semibold text-green-800 mb-2">Add New Book</h3>
                    <p class="text-green-600 text-sm">Expand the collection</p>
                </div>
            </a>

            <!-- Manage Loans -->
            <a href="{% url 'loan_list' %}" class="group relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 hover:from-orange-100 hover:to-orange-200 transition-all duration-300 card-hover">
                <div class="absolute top-0 right-0 w-20 h-20 bg-orange-500/10 rounded-full -translate-y-10 translate-x-10"></div>
                <div class="relative">
                    <div class="w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-hand-holding text-white text-lg"></i>
                    </div>
                    <h3 class="font-semibold text-orange-800 mb-2">Manage Loans</h3>
                    <p class="text-orange-600 text-sm">Track borrowing activity</p>
                </div>
            </a>
            {% endif %}

            <!-- My Profile -->
            <a href="{% url 'profile' %}" class="group relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 hover:from-purple-100 hover:to-purple-200 transition-all duration-300 card-hover">
                <div class="absolute top-0 right-0 w-20 h-20 bg-purple-500/10 rounded-full -translate-y-10 translate-x-10"></div>
                <div class="relative">
                    <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-user text-white text-lg"></i>
                    </div>
                    <h3 class="font-semibold text-purple-800 mb-2">My Profile</h3>
                    <p class="text-purple-600 text-sm">Update your information</p>
                </div>
            </a>
        </div>
    </div>

    <!-- Personal Section for Students/Teachers -->
    {% if user.can_borrow_books %}
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- My Current Loans -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">My Current Loans</h2>
            {% if my_loans %}
            <div class="space-y-3">
                {% for loan in my_loans %}
                <div class="border-l-4 border-blue-500 pl-4 py-2">
                    <h3 class="font-semibold text-gray-800">{{ loan.book.title }}</h3>
                    <p class="text-sm text-gray-600">Due: {{ loan.due_date|date:"M d, Y" }}</p>
                    {% if loan.is_overdue %}
                    <span class="inline-block px-2 py-1 text-xs font-semibold bg-red-100 text-red-800 rounded">Overdue</span>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p class="text-gray-500">No current loans</p>
            {% endif %}
        </div>

        <!-- My Reservations -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">My Reservations</h2>
            {% if my_reservations %}
            <div class="space-y-3">
                {% for reservation in my_reservations %}
                <div class="border-l-4 border-yellow-500 pl-4 py-2">
                    <h3 class="font-semibold text-gray-800">{{ reservation.book.title }}</h3>
                    <p class="text-sm text-gray-600">Reserved: {{ reservation.reservation_date|date:"M d, Y" }}</p>
                    <p class="text-sm text-gray-600">Position: #{{ reservation.priority }}</p>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p class="text-gray-500">No active reservations</p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Admin/Librarian Section -->
    {% if user.can_manage_books %}
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Loans -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Recent Loans</h2>
            {% if recent_loans %}
            <div class="space-y-3">
                {% for loan in recent_loans %}
                <div class="border-l-4 border-green-500 pl-4 py-2">
                    <h3 class="font-semibold text-gray-800">{{ loan.book.title }}</h3>
                    <p class="text-sm text-gray-600">Borrower: {{ loan.borrower.get_full_name }}</p>
                    <p class="text-sm text-gray-600">Issued: {{ loan.issue_date|date:"M d, Y" }}</p>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p class="text-gray-500">No recent loans</p>
            {% endif %}
        </div>

        <!-- Active Reservations -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Active Reservations</h2>
            {% if recent_reservations %}
            <div class="space-y-3">
                {% for reservation in recent_reservations %}
                <div class="border-l-4 border-yellow-500 pl-4 py-2">
                    <h3 class="font-semibold text-gray-800">{{ reservation.book.title }}</h3>
                    <p class="text-sm text-gray-600">User: {{ reservation.user.get_full_name }}</p>
                    <p class="text-sm text-gray-600">Reserved: {{ reservation.reservation_date|date:"M d, Y" }}</p>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p class="text-gray-500">No active reservations</p>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
