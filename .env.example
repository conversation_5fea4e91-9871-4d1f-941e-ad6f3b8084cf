# Library Management System Environment Configuration

# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,testserver

# Database Configuration
DATABASE_URL=sqlite:///db.sqlite3

# Email Configuration (for notifications)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# School Management System API Integration
# Configure these to enable school ID/PIN authentication
SCHOOL_API_URL=https://your-school-api.com/api/v1
SCHOOL_API_KEY=your-school-api-key-here

# Example School API Endpoints:
# Authentication: POST {SCHOOL_API_URL}/auth/validate
# Student Info: GET {SCHOOL_API_URL}/students/{student_id}
# Staff Info: GET {SCHOOL_API_URL}/staff/{staff_id}

# School API Request Format:
# {
#   "student_id": "STU001",
#   "pin": "1234"
# }

# Expected School API Response Format:
# {
#   "authenticated": true,
#   "student_id": "STU001",
#   "first_name": "John",
#   "last_name": "Doe",
#   "email": "<EMAIL>",
#   "phone": "+1234567890",
#   "class": "Grade 10A",
#   "type": "student"
# }

# Security Settings
SECURE_SSL_REDIRECT=False
SECURE_BROWSER_XSS_FILTER=True
SECURE_CONTENT_TYPE_NOSNIFF=True
X_FRAME_OPTIONS=DENY

# Static Files (for production)
STATIC_ROOT=/path/to/static/files
MEDIA_ROOT=/path/to/media/files

# Logging
LOG_LEVEL=INFO
LOG_FILE=library.log
