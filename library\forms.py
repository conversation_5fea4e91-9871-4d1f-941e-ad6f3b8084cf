from django import forms
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.contrib.auth import authenticate
from .models import User, Book, Author, Category, Publisher, Loan, Reservation


class CustomUserCreationForm(UserCreationForm):
    """Custom user registration form"""
    email = forms.EmailField(required=True)
    first_name = forms.CharField(max_length=30, required=True)
    last_name = forms.CharField(max_length=30, required=True)
    phone_number = forms.CharField(max_length=15, required=False)
    address = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    date_of_birth = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}), 
        required=False
    )
    enrollment_number = forms.CharField(max_length=20, required=False)
    class_grade = forms.CharField(max_length=10, required=False)
    role = forms.ChoiceField(choices=User.ROLE_CHOICES, initial='student')

    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'password1', 'password2',
                 'phone_number', 'address', 'date_of_birth', 'enrollment_number', 
                 'class_grade', 'role')

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Only admins can create admin/librarian accounts
        if user and not user.can_manage_users():
            self.fields['role'].choices = [
                ('teacher', 'Teacher'),
                ('student', 'Student'),
            ]

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.phone_number = self.cleaned_data.get('phone_number')
        user.address = self.cleaned_data.get('address')
        user.date_of_birth = self.cleaned_data.get('date_of_birth')
        user.enrollment_number = self.cleaned_data.get('enrollment_number')
        user.class_grade = self.cleaned_data.get('class_grade')
        user.role = self.cleaned_data['role']
        
        if commit:
            user.save()
        return user


class CustomAuthenticationForm(AuthenticationForm):
    """Custom login form with enhanced styling"""
    username = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Username or Email'
        })
    )
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Password'
        })
    )

    def clean(self):
        username = self.cleaned_data.get('username')
        password = self.cleaned_data.get('password')

        if username and password:
            # Try to authenticate with username first
            self.user_cache = authenticate(
                self.request, 
                username=username, 
                password=password
            )
            
            # If that fails, try with email
            if self.user_cache is None:
                try:
                    user = User.objects.get(email=username)
                    self.user_cache = authenticate(
                        self.request,
                        username=user.username,
                        password=password
                    )
                except User.DoesNotExist:
                    pass

            if self.user_cache is None:
                raise forms.ValidationError(
                    "Please enter a correct username/email and password."
                )
            else:
                self.confirm_login_allowed(self.user_cache)

        return self.cleaned_data


class UserProfileForm(forms.ModelForm):
    """Form for updating user profile"""
    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email', 'phone_number', 'address', 
                 'date_of_birth', 'enrollment_number', 'class_grade', 'profile_picture']
        widgets = {
            'address': forms.Textarea(attrs={'rows': 3}),
            'date_of_birth': forms.DateInput(attrs={'type': 'date'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field in self.fields.values():
            field.widget.attrs.update({'class': 'form-control'})


class BookForm(forms.ModelForm):
    """Form for adding/editing books"""
    authors = forms.ModelMultipleChoiceField(
        queryset=Author.objects.all(),
        widget=forms.CheckboxSelectMultiple,
        required=True
    )
    categories = forms.ModelMultipleChoiceField(
        queryset=Category.objects.all(),
        widget=forms.CheckboxSelectMultiple,
        required=True
    )

    class Meta:
        model = Book
        fields = ['title', 'subtitle', 'authors', 'isbn', 'publisher', 'publication_date',
                 'edition', 'pages', 'language', 'categories', 'cover_image',
                 'physical_description', 'shelf_location', 'section', 'floor',
                 'total_copies', 'summary', 'notes', 'price']
        widgets = {
            'publication_date': forms.DateInput(attrs={'type': 'date'}),
            'physical_description': forms.Textarea(attrs={'rows': 3}),
            'summary': forms.Textarea(attrs={'rows': 4}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            if field_name not in ['authors', 'categories']:
                field.widget.attrs.update({'class': 'form-control'})

    def save(self, commit=True):
        book = super().save(commit=False)
        if not book.available_copies:
            book.available_copies = book.total_copies
        if commit:
            book.save()
            self.save_m2m()
        return book


class BookSearchForm(forms.Form):
    """Form for searching books"""
    query = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by title, author, ISBN...'
        })
    )
    category = forms.ModelChoiceField(
        queryset=Category.objects.all(),
        required=False,
        empty_label="All Categories",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    status = forms.ChoiceField(
        choices=[('', 'All Status')] + Book.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    section = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Section'
        })
    )


class LoanForm(forms.ModelForm):
    """Form for issuing loans"""
    class Meta:
        model = Loan
        fields = ['book', 'borrower', 'due_date', 'notes']
        widgets = {
            'due_date': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Only show available books
        self.fields['book'].queryset = Book.objects.filter(
            status='available', 
            available_copies__gt=0
        )
        # Only show users who can borrow books
        self.fields['borrower'].queryset = User.objects.filter(
            role__in=['student', 'teacher'],
            is_active_member=True
        )
        
        for field in self.fields.values():
            field.widget.attrs.update({'class': 'form-control'})


class ReservationForm(forms.ModelForm):
    """Form for making reservations"""
    class Meta:
        model = Reservation
        fields = ['book', 'notes']
        widgets = {
            'notes': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Only show books that are not available
        self.fields['book'].queryset = Book.objects.filter(available_copies=0)
        
        for field in self.fields.values():
            field.widget.attrs.update({'class': 'form-control'})

    def clean_book(self):
        book = self.cleaned_data['book']
        user = self.instance.user if hasattr(self.instance, 'user') else None
        
        if user and Reservation.objects.filter(
            book=book, 
            user=user, 
            status='active'
        ).exists():
            raise forms.ValidationError("You already have an active reservation for this book.")
        
        return book
