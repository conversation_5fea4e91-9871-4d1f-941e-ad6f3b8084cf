# Generated by Django 5.2.3 on 2025-06-19 11:53

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('library', '0004_alter_loan_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('notification_type', models.CharField(choices=[('book_due', 'Book Due Soon'), ('book_overdue', 'Book Overdue'), ('book_returned', 'Book Returned'), ('book_available', 'Reserved Book Available'), ('request_approved', 'Request Approved'), ('request_rejected', 'Request Rejected'), ('fine_added', 'Fine Added'), ('general', 'General Notification')], default='general', max_length=20)),
                ('is_read', models.<PERSON>oleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('book', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='library.book')),
                ('loan', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='library.loan')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
